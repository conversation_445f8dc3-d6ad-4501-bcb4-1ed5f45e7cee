<!DOCTYPE html>
<html lang="ar" dir="rtl">
<!--begin::Head-->
<head><base href="../../../"/>
    <title>انضم لنا  | دستة</title>
    <meta charset="utf-8" />
    <meta name="description" content="The most advanced Bootstrap 5 Admin Theme with 40 unique prebuilt layouts on Themeforest trusted by 100,000 beginners and professionals. Multi-demo, Dark Mode, RTL support and complete React, Angular, Vue, Asp.Net Core, Rails, Spring, Blazor, Django, Express.js, Node.js, Flask, Symfony & Laravel versions. Grab your copy now and get life-time updates for free." />
    <meta name="keywords" content="metronic, bootstrap, bootstrap 5, angular, VueJs, React, Asp.Net Core, Rails, Spring, Blazor, Django, Express.js, Node.js, Flask, Symfony & Laravel starter kits, admin themes, web design, figma, web development, free templates, free admin themes, bootstrap theme, bootstrap template, bootstrap dashboard, bootstrap dak mode, bootstrap button, bootstrap datepicker, bootstrap timepicker, fullcalendar, datatables, flaticon" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="Metronic - Bootstrap Admin Template, HTML, VueJS, React, Angular. Laravel, Asp.Net Core, Ruby on Rails, Spring Boot, Blazor, Django, Express.js, Node.js, Flask Admin Dashboard Theme & Template" />
    <meta property="og:url" content="https://keenthemes.com/metronic" />
    <meta property="og:site_name" content="Keenthemes | Metronic" />
    <link rel="canonical" href="https://preview.keenthemes.com/metronic8" />
    <link rel="shortcut icon" href="assets/media/logos/favicon.ico" />
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <!--end::Fonts-->
    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link href="{{asset('/manager_assest/dist/assets/plugins/global/plugins.bundle.rtl.css')}}" rel="stylesheet" type="text/css" />
    <link href="{{asset('/manager_assest/dist/assets/css/style.bundle.rtl.css')}}" rel="stylesheet" type="text/css" />
    <link href="{{asset('/manager_assest/dist/assets/plugins/custom/toastr/build/toastr.css')}}" rel="stylesheet" type="text/css" />

    <!--end::Global Stylesheets Bundle-->
</head>
<!--end::Head-->
<!--begin::Body-->
<body id="kt_body" class="app-blank bgi-size-cover bgi-attachment-fixed bgi-position-center bgi-no-repeat">
<!--begin::Theme mode setup on page load-->
<script>var defaultThemeMode = "light"; var themeMode; if ( document.documentElement ) { if ( document.documentElement.hasAttribute("data-bs-theme-mode")) { themeMode = document.documentElement.getAttribute("data-bs-theme-mode"); } else { if ( localStorage.getItem("data-bs-theme") !== null ) { themeMode = localStorage.getItem("data-bs-theme"); } else { themeMode = defaultThemeMode; } } if (themeMode === "system") { themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light"; } document.documentElement.setAttribute("data-bs-theme", themeMode); }</script>
<!--end::Theme mode setup on page load-->
<!--begin::Root-->
<div class="d-flex flex-column flex-root" id="kt_app_root">
    <!--begin::Page bg image-->0
    <style>body { background-image: url({{asset('/manager_assest/dist/assets/media/auth/bg4.jpg')}}); } [data-bs-theme="dark"] body { background-image: url({{asset('/manager_assest/dist/assets/media/auth/bg4-dark.jpg')}}); }</style>
    <!--end::Page bg image-->
    <!--begin::Authentication - Sign-up -->
    <div class="d-flex flex-column flex-column-fluid flex-lg-row">
        <!--begin::Aside-->
        <div class="d-flex flex-center w-lg-50 pt-15 pt-lg-0 px-10">
            <!--begin::Aside-->
            <div class="d-flex flex-center flex-lg-start flex-column">
                <!--begin::Logo-->
                <a href="https://flowksa.com" class="mb-7">
                    <img alt="Logo" src="{{$setting->logo}}" style="width: 200px;" />
                </a>
                <!--end::Logo-->
                <!--begin::Title-->
                <h2 class="text-white fw-normal m-0">انضم للبرنامج المحاسبي الاسهل والافضل .. دستة</h2>
                <!--end::Title-->
            </div>
            <!--begin::Aside-->
        </div>
        <!--begin::Aside-->
        <!--begin::Body-->
        <div class="d-flex flex-column-fluid flex-lg-row-auto justify-content-center justify-content-lg-end p-12 p-lg-20">
            <!--begin::Card-->
            <div class="bg-body d-flex flex-column align-items-stretch flex-center rounded-4 w-md-600px p-20">
                <!--begin::Wrapper-->
                <div class="d-flex flex-center flex-column flex-column-fluid px-lg-10 pb-15 pb-lg-20">
                    <!--begin::Form-->
                    <form class="form w-100" novalidate="novalidate" id="kt_sign_up_form" data-kt-redirect-url="../../demo1/dist/authentication/layouts/creative/sign-in.html" action="{{  route('post_join_us') }}" method="post" enctype="multipart/form-data">

                        @csrf
                        <!--begin::Heading-->
                        <div class="text-center mb-11">
                            <!--begin::Title-->
                            <h1 class="text-dark fw-bolder mb-3">انضم لنا</h1>
                            <!--end::Title-->
                            <!--begin::Subtitle-->
                            <div class="text-gray-500 fw-semibold fs-6">ادخل بيانات مؤسستك</div>
                            <!--end::Subtitle=-->
                        </div>
                        <!--begin::Heading-->
                        <!--begin::Login options-->
                        <!--end::Login options-->
                        <!--begin::Separator-->
                        <div class="separator separator-content my-14">
                            <span class="w-125px text-gray-500 fw-semibold fs-7"></span>
                        </div>
                        <!--end::Separator-->

                        <!--begin::Input group=-->
                        <div class="row">

                        <div class="fv-row mb-8 col-md-6">
                            <!--begin::Email-->
                            <input type="text" placeholder="اسم المنشاءة" name="name" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Email-->
                        </div>
                        <div class="fv-row mb-8 col-md-6">
                            <!--begin::Email-->
                            <input type="text" placeholder="اسم المالك" name="name_owner" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Email-->
                        </div>
                        </div>

                        <div class="row">

                            <div class="fv-row mb-8 col-md-6">
                            <!--begin::Email-->
                            <input type="text" placeholder="رقم الجوال" name="phone" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Email-->
                        </div>
                        <div class="fv-row mb-8 col-md-6">
                            <!--begin::Email-->
                            <input type="text" placeholder="مدير المنشاءة" name="name_manager" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Email-->
                        </div>
                        </div>

                        <div class="row">

                            <div class="fv-row mb-8 col-md-6">
                            <!--begin::Email-->
                            <input type="text" placeholder="العنوان" name="location" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Email-->
                        </div>

                        <div class="fv-row mb-8 col-md-6">
                            <!--begin::Email-->
                            <input type="text" placeholder="المدينة" name="city" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Email-->
                        </div>
                        </div>
                        <div class="row">

                            <div class="fv-row mb-8 col-md-6">
                            <!--begin::Email-->
                            <input type="text" placeholder="الرمز البريدي" name="postal_code" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Email-->
                        </div>
                        <div class="fv-row mb-8 col-md-6">
                            <!--begin::Email-->
                            <input type="text" placeholder="الرقم الضريبي" name="tax_number" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Email-->
                        </div>
                        </div>

                        <!--begin::Logo upload-->
                        <div class="fv-row mb-8">
                            <!--begin::Logo upload-->
                            <div class="d-flex align-items-center">
                                <label class="form-label fw-semibold fs-6 text-gray-700 mb-3">شعار الشركة (اختياري)</label>
                            </div>
                            <input type="file" name="logo" accept="image/*" class="form-control bg-transparent" id="logo-input" />
                            <div class="text-muted fs-7 mt-1">يُسمح بالصيغ: JPEG, PNG, JPG, GIF - الحد الأقصى: 2 ميجابايت</div>
                            <div id="logo-preview" class="mt-3" style="display: none;">
                                <img id="preview-image" src="" alt="Logo Preview" style="max-width: 150px; max-height: 150px; border-radius: 8px;" />
                            </div>
                            <!--end::Logo upload-->
                        </div>
                        <!--end::Logo upload-->

                        <!--begin::Input group-->
                        <!--begin::Input group=-->
                        <div class="fv-row mb-8">
                            <!--begin::Email-->
                            <input type="text" placeholder="البريد الالكتروني" name="email" autocomplete="off" class="form-control bg-transparent" />
                            <!--end::Email-->
                        </div>
                        <!--begin::Input group-->
                        <div class="fv-row mb-8" data-kt-password-meter="true">
                            <!--begin::Wrapper-->
                            <div class="mb-1">
                                <!--begin::Input wrapper-->
                                <div class="position-relative mb-3">
                                    <input class="form-control" type="password" placeholder="كلمة المرور" name="password" autocomplete="off" />

                                </div>
                                <!--end::Input wrapper-->
                            </div>
                            <!--end::Wrapper-->
                            <!--begin::Hint-->
                            <div class="text-muted">يجب ان تكون اكبر من 8 خانات</div>
                            <!--end::Hint-->
                        </div>
                        <!--end::Input group=-->
                        <!--end::Input group=-->
                        <div class="fv-row mb-8">
                            <!--begin::Repeat Password-->
                            <input placeholder="تأكيد كلمة المرور" name="confirm-password" type="password" autocomplete="off" class="form-control" />
                            <!--end::Repeat Password-->
                        </div>
                        <!--end::Input group=-->
                        <!--begin::Accept-->
                        <!--end::Accept-->
                        <!--begin::Submit button-->
                        <div class="d-grid mb-10">
                            <button type="submit"  class="btn btn-primary">
                                <!--begin::Indicator label-->
                                <span class="indicator-label">ارسال</span>
                                <!--end::Indicator label-->
                                <!--begin::Indicator progress-->
                                <span class="indicator-progress">الرجاء الانتظار...
										<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                <!--end::Indicator progress-->
                            </button>
                        </div>
                        <!--end::Submit button-->
                        <!--begin::Sign up-->
                        <div class="text-gray-500 text-center fw-semibold fs-6">هل تمتلك حساب ؟
                            <a href="{{route('login')}}" class="link-primary fw-semibold">تسجيل دخول</a></div>
                        <!--end::Sign up-->
                    </form>
                    <!--end::Form-->
                </div>
                <!--end::Wrapper-->


            </div>
            <!--end::Card-->
        </div>
        <!--end::Body-->
    </div>
    <!--end::Authentication - Sign-up-->
</div>
<!--end::Root-->
<!--begin::Javascript-->
<script>var hostUrl = "assets/";</script>
<!--begin::Global Javascript Bundle(mandatory for all pages)-->
<script src="{{asset('/manager_assest/dist/assets/plugins/global/plugins.bundle.js')}}"></script>
<script src="{{asset('/manager_assest/dist/assets/js/scripts.bundle.js')}}"></script>
<!--end::Global Javascript Bundle-->
<!--begin::Custom Javascript(used for this page only)-->
<script src="{{asset('/manager_assest/dist/assets/js/custom/authentication/sign-up/general.js')}}"></script>
<script src="{{ asset("/manager_assest/dist/assets/plugins/custom/toastr/build/toastr.min.js") }}" type="text/javascript"></script>

<script>
    toastr.options = {
        "closeButton": true,
        "debug": false,
        "newestOnTop": false,
        "progressBar": true,
        "positionClass": "toast-top-right",
        "preventDuplicates": false,
        "onclick": null,
        "showDuration": "100",
        "hideDuration": "2000",
        "timeOut": "10000",
        "extendedTimeOut": "1000",
        "showEasing": "swing",
        "hideEasing": "linear",
        "showMethod": "fadeIn",
        "hideMethod": "fadeOut"
    };
    @if(Session::has('message'))
    toastr.{{Session::get('m-class') ? Session::get('m-class'):'success'}}("{{Session::get('message')}}");
    @endif
</script>
<!--end::Custom Javascript-->
<script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
{!! JsValidator::formRequest('\App\Http\Requests\CompanyRequest', '#kt_sign_up_form') !!}

<!-- Logo preview functionality -->
<script>
document.getElementById('logo-input').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('logo-preview');
    const previewImage = document.getElementById('preview-image');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImage.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});
</script>
<!--end::Javascript-->
</body>
<!--end::Body-->
</html>
