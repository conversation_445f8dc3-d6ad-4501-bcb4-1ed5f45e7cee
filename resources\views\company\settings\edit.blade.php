@extends('company.layout.CompanyLayout')
@section('title')
الاعدادات العامة
@endsection
@section('content')
    <div id="kt_app_content_container" class="app-container container-xxl">
        <form  class="form d-flex flex-column flex-lg-row" enctype="multipart/form-data"  action="{{  route('company.settings.updateSettings') }}"  method="post" id="form">
            @csrf
            <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                <div class="tab-content">
                    <div class="tab-pane fade show active"  role="tab-panel">
                        <div class="d-flex flex-column gap-7 gap-lg-10">
                            <div class="card card-flush py-4">

                                <div class="card-header">
                                    <div class="card-title">
                                        <h2> تعديل الاعدادات</h2>
                                    </div>
                                </div>

                                <div class="card-body pt-0">



                                    <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="required form-label">تطبيق الضريبة </label>
                                        </div>

                                        <div class="col-md-7">
                                            <select name="tax" class="form-control mb-2" required>
                                                <option value="0" @if($company->tax==0 || $company->tax==null) selected @endif>الغاء</option>
                                                <option value="1" @if($company->tax==1) selected @endif>تفعيل</option>
                                            </select>
                                        </div>

                                    </div>

                                    <!--begin::Logo upload-->
                                    <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="form-label">شعار الشركة</label>
                                        </div>

                                        <div class="col-md-7">
                                            @if($company->logo_path && file_exists(public_path($company->logo_path)))
                                                <div class="mb-3">
                                                    <img src="{{$company->logo}}" alt="Current Logo" style="max-width: 150px; max-height: 150px; border-radius: 8px;" class="mb-2" />
                                                    <div class="text-muted fs-7">الشعار الحالي</div>
                                                </div>
                                            @endif
                                            
                                            <input type="file" name="company_logo" accept="image/*" class="form-control mb-2" id="company-logo-input" />
                                            <div class="text-muted fs-7">يُسمح بالصيغ: JPEG, PNG, JPG, GIF - الحد الأقصى: 2 ميجابايت</div>
                                            
                                            <div id="company-logo-preview" class="mt-3" style="display: none;">
                                                <img id="company-preview-image" src="" alt="Logo Preview" style="max-width: 150px; max-height: 150px; border-radius: 8px;" />
                                                <div class="text-muted fs-7 mt-1">معاينة الشعار الجديد</div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Logo upload-->
{{--                                    <div class="fv-row row mb-15">--}}
{{--                                        <div class="col-md-2 d-flex align-items-center">--}}
{{--                                            <label class="required form-label">تصفير البيانات </label>--}}
{{--                                        </div>--}}

{{--                                        <div class="col-md-7">--}}
{{--                                           <a href="" >اضغط هنا</a>--}}
{{--                                        </div>--}}

{{--                                    </div>--}}


                                </div>
                            </div>

                        </div>
                    </div>

                </div>
                <div class="d-flex justify-content-end">
                    <a href="{{route('company.home')}}"  class="btn btn-light me-5">Cancel</a>
                    <button type="submit"  class="btn btn-primary">
                        <span class="indicator-label">حفظ</span>
                        <span class="indicator-progress">يرجى الانتظار...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                    </button>
                </div>
            </div>
        </form>
    </div>

@endsection

@section('script')
    <script src="{{asset('/manager_assest/dist/assets/js/custom/apps/ecommerce/catalog/save-product.js')}}"></script>
    <script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
    {!! JsValidator::formRequest('\App\Http\Requests\CompanySettingsRequest', '#form') !!}

    <!-- Company logo preview functionality -->
    <script>
    document.getElementById('company-logo-input').addEventListener('change', function(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('company-logo-preview');
        const previewImage = document.getElementById('company-preview-image');
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    });
    </script>
@endsection

