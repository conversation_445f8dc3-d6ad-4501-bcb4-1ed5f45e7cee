@extends('company.layout.CompanyLayout')
@section('title')التفاصيل
@endsection
@section('content')
    <div class="card">
        <!-- begin::Body-->
        <div class="card-body py-20">
            <!-- begin::Wrapper-->
            <div class="mw-lg-950px mx-auto w-100">
                <!-- begin::Header-->
                <div class="d-flex justify-content-between flex-column flex-sm-row mb-19">
                    <h4 class="fw-bolder text-gray-800 fs-2qx pe-5 pb-7">فاتورة مشتريات</h4>
                    <!--end::Logo-->
                    <div class="text-sm-end">
                        <!--begin::Logo-->
                        <a href="#" class="d-block mw-150px ms-sm-auto">
                            @if($companyLogo)
                            <img alt="Logo" src="{{$companyLogo}}" class="w-100" />
                        @endif
                        </a>
                        <!--end::Logo-->

                    </div>
{{--                    <div class="text-sm-end">--}}
{{--                        <a href="#" class="d-block mw-150px ms-sm-auto">--}}
{{--                            <img alt="Logo" src="{{$invoice->qr_code_image}}" class="w-100" />--}}
{{--                        </a>--}}

{{--                    </div>--}}
                </div>
                <!--end::Header-->
                <!--begin::Body-->
                <div class="pb-12">
                    <!--begin::Wrapper-->
                    <div class="d-flex flex-column gap-7 gap-md-10">
                        <!--begin::Separator-->
                        <div class="separator"></div>
                        <!--begin::Separator-->
                        <!--begin::Order details-->
                        <div class="d-flex flex-column flex-sm-row gap-7 gap-md-10 fw-bold">
                            <div class="flex-root d-flex flex-column">
                                <span class="text-muted">رقم الفاتورة</span>
                                <span class="fs-5">{{$invoice->number_code}}</span>
                            </div>
                            <div class="flex-root d-flex flex-column">
                                <span class="text-muted">التاريخ</span>
                                <span class="fs-5">{{$invoice->date}}</span>
                            </div>
                            <div class="flex-root d-flex flex-column">
                                <span class="text-muted">المورد</span>
                                <span class="fs-5">{{$invoice->supplier_id}}</span>
                            </div>

                        </div>
                        <!--end::Order details-->
                        <!--begin::Billing & shipping-->
                        <div class="d-flex flex-column flex-sm-row gap-7 gap-md-10 fw-bold">
                            <div class="flex-root d-flex flex-column">
                                <span class="text-muted">حالة الفاتورة</span>
                                <span class="fs-6">{{$invoice->payment_status}}</span>
                            </div>
                            <div class="flex-root d-flex flex-column">
                                <span class="text-muted">حالة الدفع</span>
                                <span class="fs-6">{{$invoice->payment_method}} </span>
                            </div>
                            <div class="flex-root d-flex flex-column">
                                <span class="text-muted">حساب الترحيل</span>
                                <span class="fs-6">{{$invoice->sub_account_id}}</span>
                            </div>
                        </div>
                        <div class="d-flex flex-column flex-sm-row gap-7 gap-md-10 fw-bold">

                            <div class="flex-root d-flex flex-column">
                                <span class="text-muted">التفاصيل</span>
                                <span class="fs-6">{{$invoice->notes}}</span>
                            </div>
                        </div>
                        <!--end::Billing & shipping-->
                        <!--begin:Order summary-->
                        <div class="d-flex justify-content-between flex-column">
                            <!--begin::Table-->
                            <div class="table-responsive border-bottom mb-9">
                                <table class="table align-middle table-row-dashed fs-6 gy-5 mb-0">
                                    <thead>
                                    <tr class="border-bottom fs-6 fw-bold text-muted">
                                        <th class="min-w-100px pb-2">المنتجات</th>
                                        <th class="min-w-70px text-end pb-2">وحدة القياس</th>
                                        <th class="min-w-70px text-end pb-2">المبلغ</th>

                                        <!--<th class="min-w-70px text-end pb-2"> المبلغ بعد الخصم</th>-->
                                        <th class="min-w-80px text-end pb-2">الكمية</th>
                                        <th class="min-w-100px text-end pb-2">الاجمالي</th>
                                    </tr>
                                    </thead>
                                    <tbody class="fw-semibold text-gray-600">
                                    @foreach($invoice->products as $item)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <!--begin::Thumbnail-->
                                                <a href="#" class="symbol symbol-50px">
                                                    <span class="symbol-label" style="background-image:url({{$item->image}});"></span>
                                                </a>
                                                <!--end::Thumbnail-->
                                                <!--begin::Title-->
                                                <div class="ms-5">
                                                    <div class="fw-bold">{{$item->name}}</div>
                                                </div>
                                                <!--end::Title-->
                                            </div>
                                        </td>
                                        <td class="text-end">{{$item->unit}}</td>
                                        <td class="text-end">{{$item->net_price}}</td>
                                        <!--<td class="text-end">{{$item->price}}</td>-->
                                        <td class="text-end">{{$item->count}}</td>
                                        <td class="text-end">{{$item->net_price*$item->count}}</td>
                                    </tr>
                                    @endforeach

                                    <tr>
                                        <td colspan="4" class="text-end">الاجمالي "غير شامل ضريبة القيمة المضافة" </td>
                                        <td class="text-end">{{$invoice->total}}</td>
                                    </tr>
                                     <tr>
                                        <td colspan="4" class="text-end">مبلغ الخصم</td>
                                        <td class="text-end">{{$invoice->discount_amount}}</td>
                                    </tr>
                                    <tr>
                                        <td colspan="4" class="text-end">نوع الخصم</td>
                                        @if($invoice->type_discount =='amount')
                                        <td class="text-end">مبلغ</td>
                                        @elseif($invoice->type_discount =='percentage')
                                        <td class="text-end">نسبة مئوية</td>
                                        @else
                                        <td class="text-end">لايوجد</td>
                                        @endif
                                    </tr>
                                    <tr>
                                        <td colspan="4" class="text-end">الاجمالي بعد الخصم</td>
                                        <td class="text-end">{{$invoice->total-$invoice->discount_amount}}</td>
                                    </tr>

{{--                                    <tr>--}}
{{--                                        <td colspan="4" class="text-end">الاجمالي الخاضع للضريبة</td>--}}
{{--                                        <td class="text-end">{{$invoice->total_taxable}}</td>--}}
{{--                                    </tr>--}}
                                    <tr>
                                        <td colspan="4" class="text-end">الضريبة (15%)</td>
                                        <td class="text-end">{{$invoice->tax_amount}}</td>
                                    </tr>

                                    <tr>
                                        <td colspan="4" class="fs-3 text-dark fw-bold text-end">اجمالي المدفوع</td>
                                        <td class="text-dark fs-3 fw-bolder text-end">{{$invoice->payment_amount}}</td>
                                    </tr>

                                    <tr>
                                        <td colspan="4" class="fs-3 text-dark fw-bold text-end">اجمالي المبلغ المتبقي</td>
                                        @if($invoice->payment_status!='مدفوعة')
                                        <td class="text-dark fs-3 fw-bolder text-end">{{($invoice->total_taxable+$invoice->tax_amount)-$invoice->payment_amount}}</td>
                                        @else

                                            <td class="text-dark fs-3 fw-bolder text-end">0</td>

                                        @endif
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!--end::Table-->
                        </div>
                        <!--end:Order summary-->
                    </div>
                    <!--end::Wrapper-->
                </div>
                <!--end::Body-->
                <!-- begin::Footer-->
                <div class="d-flex flex-stack flex-wrap mt-lg-20 pt-13">
                    <!-- begin::Actions-->
                    <div class="my-1 me-5">
                        <!-- begin::Pint-->
                        @can('purchases_invoice.print')
                        <a href="{{url('company/p_print/'.$invoice->id)}}" class="btn btn-success my-1">طباعة</a>
                        <!-- end::Pint-->
                        @endcan
                    </div>
                    <!-- end::Actions-->
                    <!-- begin::Action-->
                    <a href="{{route('company.purchases.invoice')}}" class="btn btn-primary my-1">رجوع</a>
                    <!-- end::Action-->
                </div>
                <!-- end::Footer-->
            </div>
            <!-- end::Wrapper-->
        </div>
        <!-- end::Body-->
    </div>
@endsection
