@extends('company.layout.CompanyLayout')
@section('title')
    تقرير قائمة الدخل
@endsection
@section('css')
    <style>
        /* ========================================
           PROFESSIONAL INCOME LIST REPORT PRINT STYLES
           ======================================== */
        
        @media print {
            /* ========================================
               PAGE SETUP & BROWSER CONTROLS
               ======================================== */
            @page {
                size: A4 portrait;
                margin: 0;
                /* Remove default browser headers and footers */
                @top-center { content: ""; }
                @top-left { content: ""; }
                @top-right { content: ""; }
                @bottom-center { content: ""; }
                @bottom-left { content: ""; }
                @bottom-right { content: ""; }
            }
            
            html, body {
                height: 100%;
                margin: 0;
                padding: 0;
                overflow: hidden;
            }
            
            /* ========================================
               CONTENT VISIBILITY CONTROL
               ======================================== */
            body * {
                visibility: hidden;
            }

            #printable-section, 
            #printable-section * {
                visibility: visible;
            }
            
            #printable-section {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                overflow: visible;
            }
            
            /* ========================================
               TYPOGRAPHY & DIRECTION
               ======================================== */
            body {
                font-family: 'Beiruti', 'DejaVu Sans', sans-serif !important;
                direction: rtl !important;
                line-height: 1.6 !important;
            }
            
            /* ========================================
               LAYOUT COMPONENTS
               ======================================== */
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                margin: 0 !important;
                padding: 20px !important;
                background: #fff !important;
                width: 100% !important;
                height: auto !important;
            }
            
            .card-header {
                background: #f8f9fa !important;
                border-bottom: 2px solid #dee2e6 !important;
                padding: 15px 20px !important;
                margin-bottom: 20px !important;
            }
            
            .card-title h2 {
                color: #000 !important;
                font-weight: bold !important;
                font-size: 24px !important;
                text-align: center !important;
                margin: 0 !important;
            }
            
            /* ========================================
               FORM ELEMENTS STYLING
               ======================================== */
            .form-control {
                border: 1px solid #ccc !important;
                background: #f9f9f9 !important;
                font-weight: bold !important;
                font-size: 16px !important;
                padding: 12px 15px !important;
                border-radius: 5px !important;
                color: #000 !important;
                display: block !important;
                visibility: visible !important;
            }
            
            .form-label {
                font-weight: 600 !important;
                color: #333 !important;
                font-size: 16px !important;
                display: block !important;
                visibility: visible !important;
            }
            
            /* ========================================
               HIDE NON-PRINTABLE ELEMENTS
               ======================================== */
            .btn,
            .date, 
            .row.mt-12 {
                display: none !important;
            }
            
            /* ========================================
               SHOW PRINTABLE CONTENT
               ======================================== */
            .tab-content,
            .tab-pane {
                display: block !important;
                visibility: visible !important;
            }
            
            /* ========================================
               DATA ROWS STYLING
               ======================================== */
            .fv-row {
                margin-bottom: 20px !important;
                border-bottom: 1px solid #eee !important;
                padding-bottom: 15px !important;
                display: flex !important;
                align-items: center !important;
            }
            
            .fv-row:last-child {
                border-bottom: 2px solid #dee2e6 !important;
                background: #f8f9fa !important;
                padding: 15px !important;
                border-radius: 5px !important;
                font-weight: bold !important;
                margin-top: 20px !important;
            }
            
            /* ========================================
               COLUMN STYLING
               ======================================== */
            .col-md-2 {
                font-weight: 600 !important;
                color: #333 !important;
                flex: 0 0 30% !important;
            }
            
            .col-md-9 {
                text-align: left !important;
                flex: 0 0 70% !important;
            }
            
            /* ========================================
               REPORT HEADER & FOOTER
               ======================================== */
            #printable-section::before {
                content: "تقرير قائمة الدخل";
                display: block;
                text-align: center;
                font-size: 20px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #2196f3;
            }
            
            #printable-section::after {
                content: "تاريخ التقرير: " attr(data-report-date);
                display: block;
                text-align: center;
                font-size: 12px;
                color: #666;
                margin-top: 20px;
                padding-top: 10px;
                border-top: 1px solid #eee;
            }
            
            /* ========================================
               PRINT-SPECIFIC ENHANCEMENTS
               ======================================== */
            .card-body {
                padding: 0 !important;
            }
            
            .tab-content {
                margin: 0 !important;
            }
            
            .d-flex {
                display: flex !important;
            }
            
            .flex-column {
                flex-direction: column !important;
            }
            
            .gap-7 {
                gap: 0 !important;
            }
            
            .gap-lg-10 {
                gap: 0 !important;
            }
            
            /* ========================================
               ENSURE DATA VISIBILITY
               ======================================== */
            .fv-row,
            .fv-row *,
            .card-body,
            .card-body * {
                visibility: visible !important;
                display: block !important;
            }
            
            .fv-row {
                display: flex !important;
            }
            
            /* ========================================
               REMOVE PAGINATION & PAGE BREAKS
               ======================================== */
            .pagination,
            .dataTables_paginate,
            .page-item,
            .page-link,
            .paginate_button,
            .dataTables_info,
            .dataTables_length,
            .dataTables_filter {
                display: none !important;
            }
            
            /* Prevent page breaks */
            table {
                page-break-inside: avoid !important;
            }
            
            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            /* Force single page */
            #printable-section {
                page-break-after: avoid !important;
                page-break-before: avoid !important;
            }
            
            /* ========================================
               FIX EMPTY SECOND PAGE
               ======================================== */
            .app-content,
            .app-container,
            .container-xxl {
                width: 100% !important;
                height: auto !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            
            /* Ensure content fits on one page */
            .card-flush {
                height: auto !important;
                min-height: auto !important;
            }
        }
    </style>
@endsection

@section('content')
    <div id="kt_app_content_container" class="app-container container-xxl">

        <!--begin::Card-->

        <div class="card card-flush">
            <!--begin::Card header-->
            <div class="card-header pt-8">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>قائمة الدخل</h2>
                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body">
        <form class="form d-flex flex-column flex-lg-row" enctype="multipart/form-data"
              action="{{  route('company.income_list_report') }}" method="get" id="form">
            @csrf
            <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">

                <div class="fv-row row mb-15 date"  >
                    <div class="col-md-1 d-flex align-items-center">
                        <label class="fs-2 form-label">من تاريخ</label>
                    </div>
                    <div class="col-md-5">
                        <input type="date" name="from_date" class="form-control mb-2" value=""/>
                    </div>

                    <div class="col-md-1 d-flex align-items-center">
                        <label class="fs-2 form-label">الى تاريخ</label>
                    </div>
                    <div class="col-md-5">
                        <input type="date" name="to_date" class="form-control mb-2" value=""/>
                    </div>
                </div>
                <div id="printable-section" data-report-title="تقرير قائمة الدخل" data-report-date="{{ now()->format('Y-m-d H:i') }}">
                <div class="tab-content">
                    <div class="tab-pane fade show active" role="tab-panel">
                        <div class="d-flex flex-column gap-7 gap-lg-10">
                            <div class="card card-flush py-4">



                                <div class="card-body pt-0">



                                    <div class="fv-row row mb-5">
                                        <div class="col-md-2 d-flex align-items-center">
                                        <label class="form-label">الايرادات "المبيعات"</label>
                                        </div>
                                        <div class="col-md-9">
                                        <input type="text" name="name" class="form-control mb-2" value="{{$net_sale}}" disabled/>
                                        </div>
                                    </div>

                                     <div class="fv-row row mb-5">
                                        <div class="col-md-2 d-flex align-items-center">
                                        <label class="form-label">مرتجع المبيعات</label>
                                        </div>
                                        <div class="col-md-9">
                                        <input type="text" name="name" class="form-control mb-2" value="{{$resale}}" disabled/>
                                        </div>
                                    </div>

                                    <div class="fv-row row mb-5">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="form-label">مجمل الربح</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" name="name" class="form-control mb-2" value="{{$net_sale-$resale}}" disabled/>
                                        </div>
                                    </div>

                                </div>

                                <div class="card-body pt-0">

                                    <div class="fv-row row mb-5">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="form-label">المشتريات</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" name="name" class="form-control mb-2" value="{{$net_purchases}}" disabled/>
                                        </div>
                                    </div>

                                </div>
                                 <div class="card-body pt-0">

                                    <div class="fv-row row mb-5">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="form-label">مرتجع المشتريات</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" name="name" class="form-control mb-2" value="{{$repurchase}}" disabled/>
                                        </div>
                                    </div>

                                </div>

                                <div class="card-body pt-0">

                                    <div class="fv-row row mb-5">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="form-label">المصروفات</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" name="name" class="form-control mb-2" value="{{$expenses}}" disabled/>
                                        </div>
                                    </div>

                                </div>

                                <div class="card-body pt-0">

                                    <div class="fv-row row mb-5">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="form-label">صافي الدخل</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" name="name" class="form-control mb-2" value="{{$net}}" disabled/>
                                        </div>
                                    </div>

                                </div>
                               </div>

                        </div>

                    </div>



                </div>
                </div>
                <!--begin::Action buttons-->
                <div class="row mt-12">
                    <div class="col-md-9 offset-md-3">
                        <!--begin::Cancel-->
                        <a href="{{route('company.home')}}" class="btn btn-light me-5">رجوع</a>
                        <!--end::Cancel-->
                        <!--begin::Button-->
                        <button  type="submit" class="btn btn-primary" >
                            <span class="indicator-label">فلترة</span>
                            <span class="indicator-progress">يرجى الانتظار...
															<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                        </button>
                        <!--end::Button-->
                        @can('income_list_report.print')
                        <button onclick="printSection()" class="btn btn-success">طباعة</button>
                        @endcan

                    </div>
                </div>
                <!--begin::Action buttons-->

            </div>

        </form>
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>

@endsection

@section('script')

    <script type="text/javascript">

        function printSection() {
            window.print();
        }

    </script>
@endsection
