<?php

namespace App\Http\Controllers;

use App\Company;
use App\Http\Requests\CompanyRequest;
use App\Models\SubAccount;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\AccountOperation;
use App\Models\Expense;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\InvoiceProductCart;
use App\Models\Receipt;
use App\Models\Repurchase;
use App\Models\Resale;

class SettingController extends Controller
{
    public function home()
    {
        return view('manager.home');
    }

    public function join_us()
    {
        return view('web.join_us');
    }

    public function post_join_us(CompanyRequest $request)
    {

        $data = $request->validated();
        $data['password'] = Hash::make($request->password);
        
        // Handle logo upload
        if ($request->hasFile('logo')) {
            try {
                $logo = $request->file('logo');
                $logoName = time() . '_' . $logo->getClientOriginalName();
                $logo->move(public_path('uploads/companies/logos'), $logoName);
                $data['logo'] = 'uploads/companies/logos/' . $logoName;
            } catch (\Exception $e) {
                // Log the error but don't fail the registration
                \Log::error('Logo upload failed: ' . $e->getMessage());
            }
        }
        
         $done= Company::query()->create($data);



          $sale=  [
            'name'=> 'المبيعات',
            'company_id'=>$done->id,
            'parent_id'=>'4',
            'ordered'=>'1',
            'balance'=>0,
            "type_account"=> "sale",

            ];

           $purchase= [

            'name'=> 'المشتريات',
            'company_id'=>$done->id,
            'parent_id'=>'4',
               'ordered'=>'1',
               'balance'=>0,
            "type_account"=> "purchase",

           ];

           $tax_sale= [

            'name'=> 'ضريبة المبيعات',
            'company_id'=>$done->id,
            'parent_id'=>'4',
               'ordered'=>'1',
               'balance'=>0,
            "type_account"=> "tax_sale",

           ];

            $tax_purchase= [

            'name'=> 'ضريبة المشتريات',
            'company_id'=>$done->id,
            'parent_id'=>'4',
                'ordered'=>'1',
                'balance'=>0,
            "type_account"=> "tax_purchase",

           ];

        $open_balance= [

            'name'=> 'افتتاحى راس مال',
            'company_id'=>$done->id,
            'parent_id'=>'0',
            'balance'=>0,
            "type_account"=> "open_balance",

        ];

        $bank_account= [
            'name'=> 'البنك',
            'company_id'=>$done->id,
            'parent_id'=> 18,
            'balance'=>0,
            'ordered'=>0,
        ];

        $cash_account= [
            'name'=> 'الخزنة',
            'company_id'=>$done->id,
            'parent_id'=> 7,
            'balance'=>0,
            'ordered'=>0,
        ];

         SubAccount::query()->create($sale);
         SubAccount::query()->create($purchase);
         SubAccount::query()->create($tax_sale);
         SubAccount::query()->create($tax_purchase);
         SubAccount::query()->create($open_balance);
         SubAccount::query()->create($bank_account);
         SubAccount::query()->create($cash_account);

        Auth::guard('company')->login($done);

        return redirect('company/home')->with('message', 'تم التسجيل بنجاح')->with('m-class', 'success');
    }


    public function  inat()
        {
            AccountOperation::truncate();
            Expense::truncate();
            Invoice::truncate();
            InvoiceProduct::truncate();
            InvoiceProductCart::truncate();
            Receipt::truncate();
            Repurchase::truncate();
            Resale::truncate();
                    return back();




        }

}
