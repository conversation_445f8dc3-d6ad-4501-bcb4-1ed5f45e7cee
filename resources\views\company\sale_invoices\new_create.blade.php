@extends('company.layout.CompanyLayout')
@section('title')انشاء فاتورة مبيعات
@endsection
@section('content')
    <div id="kt_app_content_container" class="app-container container-xxl">
        @if($errors->any())
            @foreach ($errors->all() as $error)
                <div>{{ $error }}</div>
            @endforeach
        @endif

        <!--begin::Card-->
        <div class="card card-flush">
            <!--begin::Card header-->
            <div class="card-header pt-8">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>اضافة منتج</h2>
                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body">

                <!--begin::Form-->
                <form class="form" enctype="multipart/form-data" action="{{  route('company.store.sales.invoice') }}" method="post" id="form">

                    @csrf


                    <div class="fv-row row mb-15">
                        <div class="col-md-1 d-flex align-items-center">
                            <label class="required fs-2 form-label">التاريخ</label>
                        </div>
                        <div class="col-md-5">
                            <input type="date" name="date" class="form-control mb-2" value="{{Carbon\Carbon::now()->format("Y-m-d")}}"/>
                        </div>

                        <div class="col-md-1 d-flex align-items-center">
                            <label class="required fs-2 form-label">العملاء</label>
                        </div>
                        <div class="col-md-5">
                            <select name="client_id" class="form-control mb-2" >
                                <option value="">اختر</option>
                                @foreach($clients as $client)
                                    <option value="{{$client->id}}">{{$client->name}}</option>
                                @endforeach

                            </select>
                        </div>
                    </div>


                    <div class="separator separator-dashed"></div>

                    <!--begin::Main column-->
                    <div class="d-flex flex-column flex-lg-row-fluid gap-7 gap-lg-10">
                        <!--begin::Order details-->
                        <div class="row">

                            <div class="card card-flush py-4 col-md-5">
                                <div class="card pt-4 mb-6 mb-xl-9">
                                    <!--begin::Card header-->
                                    <div class="card-header border-0">
                                        <!--begin::Card title-->
                                        <div class="card-title">
                                            <h2 class="fw-bold mb-0">المنتجات</h2>
                                        </div>
                                        <!--end::Card title-->
                                        <!--begin::Card toolbar-->
                                        <div class="card-toolbar">
                                            <div class="d-flex align-items-center position-relative my-1">
                                                <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-4">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                </i>
                                                <input type="text" name="search" id="search" class="form-control form-control-solid w-250px ps-12" placeholder="بحث" />
                                            </div>
                                        </div>
                                        <!--end::Card toolbar-->
                                    </div>
                                    <!--end::Card header-->
                                    <!--begin::Card body-->
                                    <div id="kt_customer_view_payment_method" class="card-body pt-0">

                                        <div class="separator separator-dashed"></div>
                                        <!--begin::Option-->
                                        <div class="py-0" data-kt-customer-payment-method="row">

                                            <!--begin::List widget 5-->
                                            <div class="card card-flush h-xl-100">

                                                <!--begin::Body-->
                                                <div class="card-body">
                                                    <!--begin::Scroll-->
                                                    <div class="hover-scroll-overlay-y pe-6 me-n6" style="height: 415px" id="product_list_search">
{{--                                                        <!--begin::Item-->--}}
{{--                                                        @foreach($products as $product)--}}
{{--                                                            <div class="border border-dashed border-gray-300 rounded  py-3 mb-6">--}}
{{--                                                                <!--begin::Info-->--}}
{{--                                                                <div class="d-flex flex-stack mb-3">--}}
{{--                                                                    <!--begin::Wrapper-->--}}
{{--                                                                    <div class="me-3">--}}
{{--                                                                        <!--begin::Icon-->--}}
{{--                                                                        <img src="{{$product->image}}" class="w-50px ms-n1 me-1" alt="" />--}}
{{--                                                                        <!--end::Icon-->--}}
{{--                                                                        <!--begin::Title-->--}}
{{--                                                                        <a href="#" class="text-gray-800 text-hover-primary fw-bold">{{$product->name}}</a>--}}
{{--                                                                        <!--end::Title-->--}}
{{--                                                                    </div>--}}
{{--                                                                    <!--end::Wrapper-->--}}
{{--                                                                    <!--begin::Action-->--}}
{{--                                                                    <div class="m-0">--}}
{{--                                                                        <!--begin::Menu-->--}}
{{--                                                                        <div class="position-relative d-flex align-items-center" data-kt-dialer="true" data-kt-dialer-min="1" data-kt-dialer-max="{{$product->opening_balance}}" data-kt-dialer-step="1" data-kt-dialer-decimals="0">--}}
{{--                                                                            <!--begin::Decrease control-->--}}
{{--                                                                            <button type="button" class="btn btn-icon btn-sm btn-light btn-icon-gray-400" @if($product->type=='product') onclick="decrease({{$product->id}},{{$product->opening_balance}},'product')" @else onclick="decrease({{$product->id}},{{$product->opening_balance}},'service')" @endif>--}}
{{--                                                                                <i class="ki-duotone ki-minus fs-3x"></i>--}}
{{--                                                                            </button>--}}
{{--                                                                            <!--end::Decrease control-->--}}
{{--                                                                            <!--begin::Input control-->--}}
{{--                                                                            <input type="text" class="form-control border-0 text-center px-0 fs-3 fw-bold text-gray-800 w-30px" data-kt-dialer-control="input" placeholder="Amount" name="manageBudget" readonly="readonly" value="1" id="count_{{$product->id}}" />--}}
{{--                                                                            <!--end::Input control-->--}}
{{--                                                                            <!--begin::Increase control-->--}}
{{--                                                                            <button type="button" class="btn btn-icon btn-sm btn-light btn-icon-gray-400" @if($product->type=='product') onclick="increase({{$product->id}},{{$product->opening_balance}},'product')" @else onclick="increase({{$product->id}},{{$product->opening_balance}},'service')" @endif >--}}
{{--                                                                                <i class="ki-duotone ki-plus fs-3x"></i>--}}
{{--                                                                            </button>--}}
{{--                                                                            <!--end::Increase control-->--}}
{{--                                                                        </div>--}}
{{--                                                                        <!--begin::Toolbar-->--}}
{{--                                                                        <div class="d-flex my-3 ms-9 ">--}}
{{--                                                                            <!--begin::Edit-->--}}

{{--                                                                            <a  class="btn btn-icon btn-active-light-primary w-50px h-30px me-3"  onclick="add_product_to_invoice({{$product->id}})">--}}
{{--																			<span class="fw-bold" data-bs-toggle="tooltip" data-bs-trigger="hover" title="اضافة">--}}
{{--																				اضافة--}}
{{--																			</span>--}}
{{--                                                                            </a>--}}
{{--                                                                            <!--end::Edit-->--}}

{{--                                                                        </div>--}}

{{--                                                                        <!--end::Toolbar-->--}}

{{--                                                                    </div>--}}

{{--                                                                    <!--end::Action-->--}}
{{--                                                                </div>--}}
{{--                                                                <!--end::Info-->--}}
{{--                                                                <!--begin::Customer-->--}}
{{--                                                                <div class="d-flex flex-stack">--}}
{{--                                                                    <!--begin::Name-->--}}
{{--                                                                    <span class="text-gray-400 fw-bold">الكمية المتاحة:--}}
{{--																<a href="#" class="text-gray-800 text-hover-primary fw-bold">{{$product->type=='product' ? $product->opening_balance : 'غير محدود'}}</a></span>--}}
{{--                                                                    <!--end::Name-->--}}
{{--                                                                    <!--begin::Label-->--}}
{{--                                                                    <span class="badge badge-light-success">السعر:{{$product->selling_price}}</span>--}}
{{--                                                                    <!--end::Label-->--}}
{{--                                                                </div>--}}
{{--                                                                <!--end::Customer-->--}}
{{--                                                            </div>--}}
{{--                                                        @endforeach--}}
{{--                                                        <!--end::Item-->--}}


                                                        <div class="fv-row row mb-10">

                                                            <div class="col-md-2 d-flex align-items-center">
                                                                <label class="fs-2 form-label">المنتجات</label>
                                                            </div>
                                                            <div class="col-md-8">
                                                                <select name="product" id="product" class="form-control mb-2" >
                                                                    <option value="">اختر</option>
                                                                    @foreach($products as $product)
                                                                        <option value="{{$product->id}}">{{$product->name}}  | الكمية {{$product->opening_balance}}</option>
                                                                    @endforeach

                                                                </select>
                                                            </div>
                                                        </div>



                                                        <div class="fv-row row mb-10" >
                                                            <div class="col-md-2 d-flex align-items-center">
                                                                <label class="fs-2 form-label">الكمية</label>
                                                            </div>
                                                            <div class="col-md-8">
                                                                <input name="count" id="count" class="form-control mb-2">
                                                            </div>
                                                        </div>

                                                        <div class="fv-row row mb-10" >
                                                            <div class="col-md-2 d-flex align-items-center">
                                                                <label class="fs-2 form-label">السعر</label>
                                                            </div>
                                                            <div class="col-md-8">
                                                                <input name="price" id="price" class="form-control mb-2">
                                                            </div>
                                                        </div>

                                                        <!--begin::Action buttons-->
                                                        <div class="row mt-12">
                                                            <div class="col-md-9 offset-md-3">

                                                                <!--begin::Button-->
                                                                <a   class="btn btn-primary" onclick="add_product_to_invoice()" >
                                                                    <span class="indicator-label">اضافة</span>
                                                                    <span class="indicator-progress">يرجى الانتظار...
															<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                                                </a>
                                                                <!--end::Button-->
                                                            </div>
                                                        </div>
                                                        <!--begin::Action buttons-->

                                                    </div>
                                                    <!--end::Scroll-->
                                                </div>
                                                <!--end::Body-->
                                            </div>
                                            <!--end::List widget 5-->

                                        </div>
                                        <!--end::Option-->
                                        <div class="separator separator-dashed"></div>

                                    </div>
                                    <!--end::Card body-->
                                </div>
                            </div>
                            <!--end::Order details-->
                            <!--begin::Order details-->
                            <div class="card card-flush py-4 col-md-7">

                                <div class="card card-flush py-4 flex-row-fluid overflow-hidden">
                                    <!--begin::Card header-->
                                    <div class="card-header">
                                        <div class="card-title">
                                            <h2>منتجات الفاتورة</h2>
                                        </div>
                                    </div>
                                    <!--end::Card header-->
                                    <!--begin::Card body-->
                                    <div class="card-body pt-0">
                                        <div class="table-responsive">
                                            <!--begin::Table-->
                                            <table class="table align-middle table-row-dashed fs-6 gy-5 mb-0">
                                                <thead>
                                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                    <th class="min-w-100px">اسم الصنف</th>
                                                    <th class="min-w-70px text-end">رقم الصنف</th>
                                                    <th class="min-w-70px text-end">الكمية</th>
                                                    <th class="min-w-100px text-end">سعر الوحدة</th>
                                                    <th class="min-w-100px text-end">الاجمالي</th>
                                                </tr>
                                                </thead>
                                                <tbody class="fw-semibold  text-gray-600" id="product_list">

                                                @foreach($invoice_products as $item)
                                                    <tr id="product_list_{{$item->id}}">

                                                        <td class="d-flex align-items-center">{{$item->product_name}}</td>
                                                        <td class="text-end">{{$item->product_id}}</td>
                                                        <td class="text-end">{{$item->count}}</td>
                                                        <td class="text-end">{{$item->price}}</td>
                                                        <td class="text-end">{{$item->totle}}</td>
                                                        <td class="text-end">
                                                            <a href="#" class="btn btn-icon btn-active-light-primary w-30px h-30px me-3" data-bs-toggle="tooltip" title="Delete" data-kt-customer-payment-method="delete" onclick="delete_product_from_invoice({{$item->id}})">
                                                                <i class="ki-duotone ki-trash fs-3">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                    <span class="path3"></span>
                                                                    <span class="path4"></span>
                                                                    <span class="path5"></span>
                                                                </i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                @endforeach

                                                <tr>
                                                    <td colspan="4" class="text-end">المبلغ الاجمالي</td>
                                                    <td class="text-end" id="total_price">{{$total_price}}</td>
                                                </tr>
                                                {{--                                            <tr>--}}
                                                {{--                                                <td colspan="4" class="text-end" >اجمالي الضريبة (15%)</td>--}}
                                                {{--                                                <td class="text-end" id="tax_price">{{$tax_price}}</td>--}}
                                                {{--                                            </tr>--}}

                                                <tr>
                                                    <td colspan="4" class="fs-3 text-dark text-end">صافي المبلغ</td>
                                                    <td class="text-dark fs-3 fw-bolder text-end" id="net_price">{{$net_price}}</td>
                                                </tr>



                                                </tbody>
                                            </table>
                                            <!--end::Table-->
                                        </div>
                                    </div>
                                    <!--end::Card body-->
                                </div>

                            </div>
                            <!--end::Order details-->
                        </div>

                    </div>
                    <!--end::Main column-->


                    <div class="fv-row row mb-15">


                        <div class="col-md-2 d-flex align-items-center">
                            <label class=" fs-2 form-label">نوع الضريبة</label>
                        </div>
                        <div class="col-md-9">
                            <select name="type_tax" class="form-control mb-2" >
                                <option value="">اختر</option>
                                <option value="tax_15">ضريبة 15 %</option>
                                <option value="no_tax">بدون ضريبة</option>
                                <option value="zero_tax">ضريبة صفرية</option>
                                <option value="free_tax">ضريبة معفاة</option>
                                <option value="with_tax">شامل الضريبة</option>


                            </select>
                        </div>



                    </div>

                    <div class="fv-row row mb-15">


                        <div class="col-md-2 d-flex align-items-center">
                            <label class=" fs-2 form-label">نوع الخصم</label>
                        </div>
                        <div class="col-md-9">
                            <select name="type_discount" class="form-control mb-2" >
                                <option value="">اختر</option>
                                <option value="percentage">نسبة مئوية %</option>
                                <option value="amount">مبلغ</option>

                            </select>
                        </div>



                    </div>

                    <div class="fv-row row mb-15">



                        <div class="col-md-2 d-flex align-items-center">
                            <label class=" fs-2 form-label">المبلغ</label>
                        </div>
                        <div class="col-md-9">
                            <!--begin::Input-->
                            <input type="text" class="form-control" name="discount"  />
                            <!--end::Input-->
                        </div>


                    </div>






                    <div class="fv-row row mb-15">

                        <div class="row">
                            <div class="col-md-2 d-flex align-items-center">
                                <label class="required fs-2 form-label">حالة الفاتورة</label>
                            </div>
                            <div class="col-md-9">
                                <select name="payment_status" class="form-control mb-2" id="payment_status" >
                                    <option value="">اختر</option>
                                    <option value="paid">مدفوعة</option>
                                    <option value="partly_paid">مدفوعة جزئيا</option>
                                    <option value="un_paid">غير مدفوعة</option>

                                </select>
                            </div>
                        </div>


                        <div id="payment_method_section" class="row" style="display:none">
                            <div class="col-md-2 d-flex align-items-center" >
                                <label class="required fs-2 form-label">طريقة الدفع</label>
                            </div>
                            <div class="col-md-9">
                                <select name="payment_method" class="form-control mb-2" id="payment_method" >
                                    <option value="">اختر</option>
                                    <option value="cash">نقدي</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="check">شيك</option>

                                </select>
                            </div>
                        </div>

                        <div id="sub_account_cash_section" class="row" style="display:none">
                            <div class="col-md-2 d-flex align-items-center">
                                <label class="required fs-2 form-label">النقدية</label>
                            </div>
                            <div class="col-md-9">
                                <select name="cash_sub_account_id" class="form-control mb-2"  >
                                    <option value="">اختر</option>
                                    @foreach($cashs as $cash)
                                        <option value="{{$cash->id}}">{{$cash->name}}</option>
                                    @endforeach

                                </select>
                            </div>
                        </div>

                        <div id="sub_account_bank_section" class="row" style="display:none">

                            <div class="col-md-2 d-flex align-items-center">
                                <label class="required fs-2 form-label">البنك</label>
                            </div>
                            <div class="col-md-6">
                                <select name="bank_sub_account_id" class="form-control mb-2"    >
                                    <option value="">اختر</option>
                                    @foreach($banks as $bank)
                                        <option value="{{$bank->id}}">{{$bank->name}}</option>
                                    @endforeach

                                </select>
                            </div>
                        </div>

                        <div class="fv-row row mb-15" id="payment_amount" style="display:none">
                            <div class="col-md-2 d-flex align-items-center">
                                <label class="fs-2 form-label">المبلغ المدفوع</label>
                            </div>
                            <div class="col-md-6">
                                <input name="payment_amount" class="form-control mb-2">
                            </div>
                        </div>



                    </div>




                    <div class="fv-row row mb-15">
                        <div class="col-md-2 d-flex align-items-center">
                            <label class="fs-2 form-label">الملاحظات</label>
                        </div>
                        <div class="col-md-9">
                            <textarea name="notes" class="form-control mb-2"></textarea>
                        </div>
                    </div>

                    <div class="fv-row row mb-15">
                        <div class="col-md-2 d-flex align-items-center">
                            <label class="fs-2 form-label">الصورة</label>
                        </div>
                        <div class="col-md-9">
                            <input type="file" name="image" class="form-control mb-2">
                        </div>
                    </div>


                    <div class="fv-row row mb-15">


                        <div class="col-md-2 d-flex align-items-center">
                            <label class=" fs-2 form-label">حالة الفاتورة</label>
                        </div>
                        <div class="col-md-9">
                            <select name="type" class="form-control mb-2" >
                                <option value="">اختر</option>
                                <option value="wating">حفظ كمسودة</option>
                                <option value="done">اعتماد</option>

                            </select>
                        </div>



                    </div>




                    <div class="separator separator-dashed"></div>



                    <!--begin::Action buttons-->
                    <div class="row mt-12">
                        <div class="col-md-9 offset-md-3">
                            <!--begin::Cancel-->
                            <a href="{{route('company.sales.invoice')}}" class="btn btn-light me-5">رجوع</a>
                            <!--end::Cancel-->
                            <!--begin::Button-->
                            <button  type="submit" class="btn btn-primary" >
                                <span class="indicator-label">حفظ</span>
                                <span class="indicator-progress">يرجى الانتظار...
															<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                            </button>
                            <!--end::Button-->
                        </div>
                    </div>
                    <!--begin::Action buttons-->
                </form>
                <!--end::Form-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>

@endsection
@section('script')

    <script src="{{asset('/manager_assest/dist/assets/js/custom/apps/ecommerce/catalog/save-product.js')}}"></script>
    <script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
    {!! JsValidator::formRequest('\App\Http\Requests\SalesInvoiceRequest', '#form') !!}

    <script>


        function add_product_to_invoice(id) {

            let product_id= $("#product").val();
            let count= $("#count").val();
            let price= $("#price").val();



            request = $.ajax({
                url: "{{url('company/add_product_to_invoice')}}",
                type: "get",
                data:{
                    'count':count,
                    'product_id': product_id,
                    'price': price
                },
                success: function (response) {


                    console.log(response);

                    if (response.message == 'count_low') {
                        alert('الكمية المطلوبة اكبر من المتوفرة في المخزون')
                    }

                    if (response.success == true) {

                        $("#count").val('');
                        $("#product").val('');
                        $("#price").val('');


                        $('#product_list').prepend(response.page)
                        $('#total_price').text(response.total_price);
                        $('#tax_price').text(response.tax_price);
                        $('#net_price').text(response.net_price);
                    }
                    else {

                    }
                },
            });







        }
        function delete_product_from_invoice(id) {
            console.log(id);
            var url = '{{url("company/delete_product_from_invoice")}}/' + id;
            var csrf_token = '{{csrf_token()}}';
            $.ajax({
                type: 'delete',
                headers: {'X-CSRF-TOKEN': csrf_token},
                url: url,
                data: {_method:'delete'},
                success: function (response) {
                    console.log(response);
                    if (response.success == true) {
                        $('#product_list_' + id).hide(500);
                        $('#total_price').text(response.total_price);
                        $('#tax_price').text(response.tax_price);
                        $('#net_price').text(response.net_price);

                    } else {

                    }
                },
                error: function (e) {

                }
            });

        }
        $(document).on('change','#payment_status', function(){
            var payment_status = $(this).val();

            if(payment_status == 'un_paid'){

                $('#payment_amount').hide();
                $('#payment_method_section').hide();
                $('#sub_account_cash_section').hide();
                $('#sub_account_bank_section').hide();
            }else if(payment_status == 'partly_paid') {

                $('#payment_amount').show();
                $('#payment_method_section').show();
                $('#sub_account_cash_section').hide();
                $('#sub_account_bank_section').hide();

            }else{
                $('#payment_amount').hide();
                $('#payment_method_section').show();
                $('#sub_account_cash_section').hide();
                $('#sub_account_bank_section').hide();


            }
        })
        $(document).on('change','#payment_method', function(){
            var payment_status = $(this).val();

            if(payment_status == 'cash'){


                $('#sub_account_cash_section').show();
                $('#sub_account_bank_section').hide();
            }else {

                $('#sub_account_cash_section').hide();
                $('#sub_account_bank_section').show();

            }
        })
        $('#search').keyup(function(){
            var csrf_token = '{{csrf_token()}}';

            var url = '{{url("/company/search_product")}}';


            $.ajax({
                type: 'get',
                headers: {'X-CSRF-TOKEN': csrf_token},
                url: url,
                data: {
                    search:$("#search").val()
                },

                success: function (data) {

                    if(data.success == true) {
                        if(data.page !== null){
                            $("#product").html('');
                            $("#product").append(data.page);



                        }else{



                        }}



                },
                error: function (e) {
                    // swal('exception', {icon: "error"});
                }
            });

        });




        function increase(id,max,type){

            var osa=  $('#count_'+ id).val();

            if(type=='product') {


                if (osa == max) {

                    alert('انتهت الكمية في المخزون')
                } else {

                    osa++;

                }


                var oij = osa

                $('#count_' + id).val(oij);

            }else {


                osa++;
                var oij = osa
                $('#count_' + id).val(oij);



            }



        }
        function decrease(id,max,type){

            var osa=  $('#count_'+ id).val();



            if (osa == 1) {

                alert('يجب ان تكون الكمية اكبر من 1')
            } else {

                osa--;

            }


            var oij = osa

            $('#count_' + id).val(oij);

        }

        $(document).on('change','#product', function(){
            var product_price = $('#product').val();

            request = $.ajax({
                url: "{{url('company/get_price_product/')}}/"+product_price,
                type: "get",
                success: function (response) {

                    if (response.success == true) {
                        $('#price').val(response.price);

                    }
                    else {

                    }
                },
            });




        })



    </script>
@endsection
