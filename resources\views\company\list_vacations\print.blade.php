<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>طباعة اجازة</title>
    <!-- Stylesheets -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Beiruti:wght@200..900&display=swap');


        *,
        *:before,
        *:after {
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
        }

        * {
            margin: 0px;
            padding: 0px;
            border: none;
            outline: none;
        }

        :root {
            --main-color: #AC441E;
            --sub-color: #28323C;
            --sec-padding: 80px 0;
        }

        body {
            font-family: Deja<PERSON><PERSON>, sans-serif;
            direction: rtl;
        }

        ul {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-family: "Beiruti", sans-serif;
            font-weight: 400;
            font-style: normal;
            margin: 0;
        }

        p {
            margin: 0;
        }

        html,
        button,
        input,
        select,
        textarea {
            color: #222;
        }

        ::-moz-selection {
            background: #b3d4fc;
            text-shadow: none;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        img {
            vertical-align: middle;

        }

        fieldset {
            border: 0;
            margin: 0;
            padding: 0;
        }

        textarea {
            resize: vertical;
        }


        a,
        button {
            -webkit-transition: all 0.4s ease;
            -moz-transition: all 0.4s ease;
            -ms-transition: all 0.4s ease;
            -o-transition: all 0.4s ease;
            transition: all 0.4s ease;
        }

        p {
            margin: 0;
        }

        a {
            cursor: pointer !important;
            text-decoration: none !important;
        }

        a:hover,
        a:active,
        a:focus,
        a:visited {
            text-decoration: none !important;
        }

        input,
        textarea,
        a,
        button {
            outline: none !important;
            text-decoration: none;
        }

        img {
            max-width: 100%;
        }

        .main-wrapper {
            position: relative;
            min-height: 100%;
            overflow: hidden;
            padding-top: 65px;
        }

        .menu-toggle .main-wrapper {
            -webkit-transition: -webkit-transform .0s ease;
            transition: -webkit-transform .0s ease;
            transition: transform .0s ease;
            transition: transform .0s ease,
            -webkit-transform .0s ease;
        }

        .container {
            width: 90%;
            max-width: 800px;
            margin: 20px auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 1px 7px rgb(0 0 0 / 16%);
            border-radius: 8px;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #000;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 2px;
            font-size: 10px;
        }
        .text-center {
            text-align: center;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            font-size: 17px;
            box-sizing: border-box;
            font-family: 'Beiruti';
        }
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }
        .form-group > label {
            font-size: 18px;
            color: #000;
            font-weight: 600;
            display: block;
            margin-bottom: 10px;
        }
        .img-att-a img {
            width: 100px;
            height: 100px;
            object-fit: cover;
        }
        .img-att img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        .text-end {
            text-align: end;
        }
        .btn-submit {
            background-color: #468803;
            color: #fff;
            border: none;
            padding: 10px 20px;
            font-size: 1rem;
            cursor: pointer;
            border-radius: 5px;
            margin-top: 15px;
            font-family: 'Beiruti';
            margin-right: auto;
        }
        .btn-submit:hover {
            background-color: #406e12;
        }
        .add-row {
            position: absolute;
            top: 0;
            left: 0;
        }
        .add-row a {
            background: #e2e2e2;
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: 500;
            color: #3c3c3c;
        }


        .cbx span {
            display: inline-block;
            vertical-align: middle;
            border: 0;
            transform: translate3d(0, 0, 0);
        }

        .cbx span {
            position: relative;
            width: 17px;
            height: 17px;
            background: #eee;
            border-radius: 3px;
            border: 0;
            transform: scale(1);
            vertical-align: middle;
            transition: all 0.2s ease;
            top: 0;
        }

        .cbx span svg {
            position: absolute;
            z-index: 1;
            top: 5px;
            left: 3px;
            fill: none;
            stroke: white;
            stroke-width: 2;
            stroke-linecap: round;
            stroke-linejoin: round;
            stroke-dasharray: 16px;
            stroke-dashoffset: 16px;
            transition: all 0.3s ease;
            transition-delay: 0.1s;
            transform: translate3d(0, 0, 0);
        }

        .cbx span:before {
            content: "";
            width: 100%;
            height: 100%;
            background: #468803;
            display: block;
            transform: scale(0);
            opacity: 1;
            border-radius: 50%;
            transition-delay: 0.2s;
        }

        .cbx span {
            margin-left: 8px;
        }

        .cbx span:after {
            content: "";
            position: absolute;
            top: 8px;
            left: 0;
            height: 0;
            width: 100%;
            background: #468803;
            transform-origin: 0 0;
            transform: scaleX(0);
        }

        .cbx:hover span {
            border-color: #468803;
        }

        .inp-cbx:checked + .cbx span {
            border-color: #468803;
            background: #468803;
            animation: check 0.6s ease;
        }

        .inp-cbx:checked + .cbx span svg {
            stroke-dashoffset: 0;
        }

        .inp-cbx:checked + .cbx span:before {
            transform: scale(2.2);
            opacity: 0;
            transition: all 0.6s ease;
        }

        .inp-cbx:checked + .cbx span {
            color: #B9B8C3;
            transition: all 0.3s ease;
        }

        .inp-cbx:checked + .cbx span:after {
            transform: scaleX(1);
            transition: all 0.3s ease;
        }

        @keyframes check {
            50% {
                transform: scale(1.2);
            }
        }
        @media print {

            @page {
                size: A4 landscape;
                max-height: 100%;
                max-width: 100%


            }


            @page {
                margin: 0mm 0mm 0mm 0mm;
            }

        }
    </style>
</head>

<body>

<div class="main-wrapper">

    <section class="section_home">
        <div class="container">
            <h1>طباعة اجازة : {{$list_vacations->date}}</h1>
            <form class="form-modal" action="#" method="post">
                <div class="form-group">
                    <table>
                        <tr>
                            <td>اسم الموضف :<br> {{$list_vacations->create_name}} </td>
                            <td>الرصيد المتبقي :<br> {{$list_vacations->balance}} </td>

                        </tr>
                    </table>
                </div>
                <div class="form-group">
                    <div class="row">

                    </div>
                    <table>
                        <tr>
                            <td>نوع الاجازة</td>
                            <td>تاريخ البدء</td>
                            <td>تاريخ الانتهاء</td>
                            <td>عدد الايام</td>


                        </tr>
                        <tr>
                            <td>{{$list_vacations->type_vic}}</td>
                            <td>{{$list_vacations->start_date}}</td>
                            <td>{{$list_vacations->end_date}}</td>
                            <td>{{$list_vacations->number_days}}</td>


                        </tr>
                    </table>
                </div>

            </form>
        </div>
    </section>
    <!--section_home-->

</div>
<!--main-wrapper-->
<script type="text/javascript">

    setTimeout(() => {
        window.print();
    }, 1000); // انتظر قليلاً حتى يتم تحديث المحتوى

</script>

</body>
</html>
