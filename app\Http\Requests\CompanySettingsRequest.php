<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CompanySettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'tax' => 'required|in:0,1',
            'company_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'tax.required' => 'حقل الضريبة مطلوب',
            'tax.in' => 'قيمة الضريبة يجب أن تكون 0 أو 1',
            'company_logo.image' => 'ملف الشعار يجب أن يكون صورة',
            'company_logo.mimes' => 'صيغة الشعار يجب أن تكون: jpeg, png, jpg, gif',
            'company_logo.max' => 'حجم الشعار يجب أن لا يتجاوز 2 ميجابايت',
        ];
    }
}
