<?php

namespace App\Models;

use App\Company;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;



class Payroll extends Model
{

    use HasFactory, SoftDeletes ;
    protected $fillable = [
        'date','insurance_discount','extra_hour','extra_salary','reward',
        'discount','net_sal','hour_salary','daly_salary','notes','created_by','employee_id'
    ];

    protected $appends=['employee_name','employee_number','totel_salary','housing_allowance'];
    public function getEmployeeNameAttribute()
    {
        $employee=Company::query()->where('id',$this->employee_id)->first();
        if ($employee) {

            return $employee->name;
        }else{

            return  'لايوجد';
        }
    }
    public function getHousingAllowanceAttribute()
    {
        $employee=Company::query()->where('id',$this->employee_id)->first();
        if ($employee) {

            return ($employee->basic_salary+$employee->housing_allowance)*0.0975;
        }else{

            return  '';
        }
    }
    public function getTotelSalaryAttribute()
    {
        $employee=Company::query()->where('id',$this->employee_id)->first();
        if ($employee) {

            return $employee->total_salary;
        }else{

            return  '';
        }
    }
    public function getEmployeeNumberAttribute()
    {
        $employee=Company::query()->where('id',$this->getRawOriginal('created_by'))->first();
        if ($employee) {

            return $employee->job_number;
        }else{

            return  'لايوجد';
        }
    }
    public function getCreatedByAttribute($value)
    {
        $employee=Company::query()->where('id',$value)->first();
        if ($employee) {

            return $employee->name;
        }else{

            return  'لايوجد';
        }
    }

}
