@extends('company.layout.CompanyLayout')
@section('title')
    عمليات الحساب
@endsection
@section('css')
    <style>
        /* ========================================
           PROFESSIONAL ACCOUNT STATEMENT FILTER PRINT STYLES
           ======================================== */
        
        @media print {
            /* ========================================
               PAGE SETUP & BROWSER CONTROLS
               ======================================== */
            @page {
                size: A4 landscape;
                margin: 10mm;
                /* Remove default browser headers and footers */
                @top-center { content: ""; }
                @top-left { content: ""; }
                @top-right { content: ""; }
                @bottom-center { content: ""; }
                @bottom-left { content: ""; }
                @bottom-right { content: ""; }
            }
            
            html, body {
                height: 100%;
                margin: 0;
                padding: 0;
                overflow: hidden;
            }
            
            /* ========================================
               CONTENT VISIBILITY CONTROL
               ======================================== */
            body * {
                visibility: hidden;
            }

            #printable-section, 
            #printable-section * {
                visibility: visible;
            }
            
            #printable-section {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                overflow: visible;
            }
            
            /* ========================================
               TYPOGRAPHY & DIRECTION
               ======================================== */
            body {
                font-family: 'Beiruti', 'DejaVu Sans', sans-serif !important;
                direction: rtl !important;
                line-height: 1.3 !important;
            }
            
            /* ========================================
               LAYOUT COMPONENTS
               ======================================== */
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                margin: 0 !important;
                padding: 15px !important;
                background: #fff !important;
                width: 100% !important;
                height: auto !important;
            }
            
            .card-header {
                background: #f8f9fa !important;
                border-bottom: 2px solid #dee2e6 !important;
                padding: 10px 15px !important;
                margin-bottom: 15px !important;
            }
            
            /* ========================================
               HIDE NON-PRINTABLE ELEMENTS
               ======================================== */
            .btn,
            .fv-row,
            .form-control,
            .form-label {
                display: none !important;
            }
            
            /* ========================================
               TABLE STYLING - UNIFIED BLUE THEME
               ======================================== */
            table {
                width: 100% !important;
                border-collapse: collapse !important;
                margin: 10px 0 !important;
                font-size: 11px !important;
                text-align: center !important;
            }
            
            th, td {
                border: 1px solid #ddd !important;
                padding: 6px 8px !important;
                text-align: center !important;
                vertical-align: middle !important;
            }
            
            th {
                background: #e3f2fd !important;
                font-weight: bold !important;
                color: #1976d2 !important;
                font-size: 10px !important;
                border-bottom: 2px solid #2196f3 !important;
            }
            
            td {
                font-size: 10px !important;
                color: #000 !important;
            }
            
            /* ========================================
               CHECKBOX COLUMN HIDING
               ======================================== */
            th:first-child,
            td:first-child {
                display: none !important;
            }
            
            /* ========================================
               UNIFIED DATA STYLING
               ======================================== */
            .table {
                border: 1px solid #dee2e6 !important;
            }
            
            .table thead th {
                background: #e3f2fd !important;
                border-bottom: 2px solid #2196f3 !important;
                color: #1976d2 !important;
            }
            
            .table tbody tr:nth-child(even) {
                background: #f8f9fa !important;
            }
            
            .table tbody tr:hover {
                background: #e3f2fd !important;
            }
            
            /* ========================================
               AMOUNT COLUMN STYLING
               ======================================== */
            td:nth-child(4), /* مدين column */
            td:nth-child(5) { /* دائن column */
                font-weight: bold !important;
                color: #2e7d32 !important;
                text-align: center !important;
            }
            
            /* ========================================
               REPORT HEADER & FOOTER
               ======================================== */
            #printable-section::before {
                content: "عمليات الحساب";
                display: block;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 15px;
                padding-bottom: 8px;
                border-bottom: 2px solid #2196f3;
            }
            
            #printable-section::after {
                content: "تاريخ التقرير: " attr(data-report-date);
                display: block;
                text-align: center;
                font-size: 10px;
                color: #666;
                margin-top: 15px;
                padding-top: 8px;
                border-top: 1px solid #eee;
            }
            
            /* ========================================
               PRINT-SPECIFIC ENHANCEMENTS
               ======================================== */
            .card-body {
                padding: 0 !important;
            }
            
            .align-middle {
                vertical-align: middle !important;
            }
            
            .text-start {
                text-align: center !important;
            }
            
            .text-gray-400 {
                color: #666 !important;
            }
            
            .fw-bold {
                font-weight: bold !important;
            }
            
            .fs-7 {
                font-size: 10px !important;
            }
            
            .text-uppercase {
                text-transform: none !important;
            }
            
            .gs-0 {
                gap: 0 !important;
            }
            
            .pe-2 {
                padding-right: 0 !important;
            }
            
            .min-w-100px {
                min-width: auto !important;
            }
            
            .text-gray-800 {
                color: #000 !important;
            }
            
            .fs-5 {
                font-size: 10px !important;
            }
            
            /* ========================================
               REMOVE PAGINATION & PAGE BREAKS
               ======================================== */
            .pagination,
            .dataTables_paginate,
            .page-item,
            .page-link,
            .paginate_button,
            .dataTables_info,
            .dataTables_length,
            .dataTables_filter {
                display: none !important;
            }
            
            /* Prevent page breaks */
            table {
                page-break-inside: avoid !important;
            }
            
            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            /* Force single page */
            #printable-section {
                page-break-after: avoid !important;
                page-break-before: avoid !important;
            }
            
            /* ========================================
               FIX EMPTY SECOND PAGE
               ======================================== */
            .app-content,
            .app-container,
            .container-xxl {
                width: 100% !important;
                height: auto !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            
            /* Ensure content fits on one page */
            .card-flush {
                height: auto !important;
                min-height: auto !important;
            }
            
            /* ========================================
               HANDLE CONTENT OVERFLOW
               ======================================== */
            /* Allow content to flow to next page if needed */
            #printable-section {
                overflow: visible !important;
                height: auto !important;
                min-height: auto !important;
            }
            
            /* Ensure tables can break across pages if needed */
            table {
                page-break-inside: auto !important;
            }
            
            /* Allow rows to break if table is too large */
            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            /* Reduce font sizes for better fit */
            th {
                font-size: 9px !important;
                padding: 4px 6px !important;
            }
            
            td {
                font-size: 9px !important;
                padding: 4px 6px !important;
            }
            
            /* Reduce margins for more content space */
            @page {
                margin: 5mm !important;
            }
            
            /* Ensure all content is visible */
            .card {
                overflow: visible !important;
                height: auto !important;
                min-height: auto !important;
            }
            
            /* Allow content to scale down if needed */
            body {
                font-size: 90% !important;
            }
            
            /* Ensure no content is hidden */
            * {
                overflow: visible !important;
            }
            
            /* ========================================
               ENSURE COLOR PRINTING
               ======================================== */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
        }
    </style>
@endsection

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Products-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">

                    </div>
                    <!--end::Card title-->

                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <div class="fv-row row mb-15">

                    </div>
                    <!--begin::Table-->
                    <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                        <!--begin::Col-->

                        <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                            <!--begin::Card widget 20-->
                            <!--end::Card widget 20-->
                            <!--begin::Card widget 7-->
                            <div class="card card-flush h-md-100 mb-5 mb-xl-10">
                                <!--begin::Header-->
                                <div class="card-header pt-5">
                                    <!--begin::Title-->
                                    <div class="card-title d-flex flex-column">
                                        <!--begin::Amount-->
                                        <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{$debtor}}</span>
                                        <!--end::Amount-->
                                        <!--begin::Subtitle-->
                                        <span class="text-gray-400 pt-1 fw-semibold fs-6">مجموع المدين</span>
                                        <!--end::Subtitle-->
                                    </div>
                                    <!--end::Title-->
                                </div>
                                <!--end::Header-->
                                <!--begin::Card body-->

                                <!--end::Card body-->
                            </div>
                            <!--end::Card widget 7-->
                        </div>
                        <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                            <!--begin::Card widget 20-->
                            <!--end::Card widget 20-->
                            <!--begin::Card widget 7-->
                            <div class="card card-flush h-md-100 mb-5 mb-xl-10">
                                <!--begin::Header-->
                                <div class="card-header pt-5">
                                    <!--begin::Title-->
                                    <div class="card-title d-flex flex-column">
                                        <!--begin::Amount-->
                                        <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{$creditor}}</span>
                                        <!--end::Amount-->
                                        <!--begin::Subtitle-->
                                        <span class="text-gray-400 pt-1 fw-semibold fs-6">مجموع الدائن </span>
                                        <!--end::Subtitle-->
                                    </div>
                                    <!--end::Title-->
                                </div>
                                <!--end::Header-->
                                <!--begin::Card body-->

                                <!--end::Card body-->
                            </div>
                            <!--end::Card widget 7-->
                        </div>
                        <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                            <!--begin::Card widget 20-->
                            <!--end::Card widget 20-->
                            <!--begin::Card widget 7-->
                            <div class="card card-flush h-md-100 mb-5 mb-xl-10">
                                <!--begin::Header-->
                                <div class="card-header pt-5">
                                    <!--begin::Title-->
                                    <div class="card-title d-flex flex-column">
                                        <!--begin::Amount-->
                                        <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2">{{$debtor-$creditor}}</span>
                                        <!--end::Amount-->
                                        <!--begin::Subtitle-->
                                        <span class="text-gray-400 pt-1 fw-semibold fs-6">الرصيد النهائي</span>
                                        <!--end::Subtitle-->
                                    </div>
                                    <!--end::Title-->
                                </div>
                                <!--end::Header-->
                                <!--begin::Card body-->

                                <!--end::Card body-->
                            </div>
                            <!--end::Card widget 7-->
                        </div>



                        <!--end::Col-->
                    </div>
                    @can('account_statement.print')
                    <button onclick="printSection()" class="btn btn-success">طباعة</button>
                    @endcan

                    <div id="printable-section">
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="datatable">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px">التاريخ</th>
                            <th class="min-w-100px">التفاصيل</th>
                            <th class="min-w-100px">مدين</th>
                            <th class="min-w-100px">دائن</th>
                            <th class="min-w-100px">الحساب</th>
                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">

                        @forelse ($data as $row)
                        <tr class="odd">
                            <td class="dtr-control">
                                <div class="form-check form-check-sm form-check-custom form-check-solid">

                                </div>
                            </td>
                            <td class="text-gray-800 fs-5 fw-bold">{{$row->date}}</td>
                            <td class="text-gray-800 fs-5 fw-bold">{{$row->details}}</td>
                            <td class="text-gray-800 fs-5 fw-bold">{{$row->debtor}}</td>
                            <td class="text-gray-800 fs-5 fw-bold">{{$row->creditor}}</td>
                            <td class="text-gray-800 fs-5 fw-bold">{{$row->account_name}}</td>

                        @empty
                            <p>No data</p>
                        @endforelse


                        </tbody>
                    </table>
                    </div>
                    <!--end::Table-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->
        </div>
        <!--end::Content container-->
    </div>




@endsection
@section('script')










@endsection


