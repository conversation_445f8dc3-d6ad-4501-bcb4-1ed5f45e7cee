<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\AccountOperation;
use App\Models\Client;
use App\Models\Expense;
use App\Models\Invoice;
use App\Models\MainAccount;
use App\Models\Product;
use App\Models\Resale;
use App\Models\Repurchase;
use App\Models\SubAccount;
use App\Models\Supplier;
use Yajra\DataTables\DataTables;
use Auth;
use Illuminate\Http\Request;



class ReportController extends Controller
{



    public function income_list_report(Request $request){

        $this->authorize('income_list_report.index');

        $company_id=Auth::guard('company')->user()->id;

        // Initialize default values
        $net_sale = 0;
        $net_purchases = 0;
        $resale = 0;
        $repurchase = 0;
        $expenses = 0;
        $net = 0;

        // Get sale sub account
        $sub_account_sale = SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();
        if ($sub_account_sale) {
            $sales = AccountOperation::query()->where('sub_account', $sub_account_sale->id);
            $resale_query = AccountOperation::query()->where('sub_account', $sub_account_sale->id);
        } else {
            $sales = AccountOperation::query()->where('id', 0); // Empty query
            $resale_query = AccountOperation::query()->where('id', 0); // Empty query
        }

        // Get purchase sub account
        $sub_account_purchase = SubAccount::query()->where('type_account','purchase')->where('company_id',$company_id)->first();
        if ($sub_account_purchase) {
            $purchases = AccountOperation::query()->where('sub_account', $sub_account_purchase->id);
            $repurchase_query = AccountOperation::query()->where('sub_account', $sub_account_purchase->id);
        } else {
            $purchases = AccountOperation::query()->where('id', 0); // Empty query
            $repurchase_query = AccountOperation::query()->where('id', 0); // Empty query
        }

        $expenses_query = Expense::query()->where('company_id',$company_id);

        if(!is_null($request->from_date)) {
            if ($sub_account_sale) {
                $sales = $sales->whereDate('date','>=',$request->from_date);
                $resale_query = $resale_query->whereDate('date','>=',$request->from_date);
            }
            if ($sub_account_purchase) {
                $purchases = $purchases->whereDate('date','>=',$request->from_date);
                $repurchase_query = $repurchase_query->whereDate('date','>=',$request->from_date);
            }
            $expenses_query = $expenses_query->whereDate('date','>=',$request->from_date);
        }
        
        if(!is_null($request->to_date)) {
            if ($sub_account_sale) {
                $sales = $sales->whereDate('date','<=',$request->to_date);
                $resale_query = $resale_query->whereDate('date','<=',$request->to_date);
            }
            if ($sub_account_purchase) {
                $purchases = $purchases->whereDate('date','<=',$request->to_date);
                $repurchase_query = $repurchase_query->whereDate('date','<=',$request->to_date);
            }
            $expenses_query = $expenses_query->whereDate('date','<=',$request->to_date);
        }

        if (!is_null($request->from_date) && !is_null($request->to_date) ){
            if ($sub_account_sale) {
                $sales = $sales->whereBetween('date',[$request->from_date,$request->to_date]);
                $resale_query = $resale_query->whereBetween('date',[$request->from_date,$request->to_date]);
            }
            if ($sub_account_purchase) {
                $purchases = $purchases->whereBetween('date',[$request->from_date,$request->to_date]);
                $repurchase_query = $repurchase_query->whereBetween('date',[$request->from_date,$request->to_date]);
            }
            $expenses_query = $expenses_query->whereBetween('date',[$request->from_date,$request->to_date]);
        }

        // Calculate sums only if data exists
        if ($sub_account_sale) {
            $net_sale = $sales->sum('creditor');
            $resale = $resale_query->sum('debtor');
        }
        
        if ($sub_account_purchase) {
            $net_purchases = $purchases->sum('debtor');
            $repurchase = $repurchase_query->sum('creditor');
        }
        
        $expenses = $expenses_query->sum('amount');

        $net = ($net_sale + $repurchase) - ($net_purchases + $expenses + $resale);

        return view('company.reports.income_list_report',compact('net_sale','net_purchases','resale','repurchase','expenses','net'));


    }


    public function account_statement()
    {
        $this->authorize('account_statement.index');

        $company_id=Auth::guard('company')->user()->id;
//
        $clients = Client::query()->where('company_id',$company_id)->get();
        $suppliers = Supplier::query()->where('company_id',$company_id)->get();
//
//
//        $invoiceIds = Invoice::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $clientIds = Client::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $supplierIds = Supplier::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $expenseIds = Expense::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $resaleds = Resale::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $repurchaseds = Repurchase::query()->where('company_id',$company_id)->pluck('id')->toArray();
          $subAccounts = SubAccount::query()->where('company_id',$company_id)->get();
//
//
//
//
//        $arry_data=array_merge($invoiceIds,$clientIds,$supplierIds,$expenseIds,$resaleds,$repurchaseds,$subAccounts);

        $balance=0;
        $creditor=AccountOperation::query()->where('company_id',$company_id)->sum('creditor');;
        $debtor=AccountOperation::query()->where('company_id',$company_id)->sum('debtor');


        if (request()->ajax())
        {

            $data = AccountOperation::query()->where('company_id',$company_id)->latest();

            // Apply all possible filters (unified approach)
            $search = request()->get('search', false);
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);
            $sub_account_id = request()->get('sub_account_id', false);
            $client = request()->get('client', false);
            $supplier = request()->get('supplier', false);

            // Search filter
            if(!empty($search) && $search != '') {
                $data = $data->where('details', 'like', '%' . $search. '%');
            }

            // Date filters (improved logic)
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Sub account filters (from account_statement_filter logic)
            if(!empty($sub_account_id) && $sub_account_id != '') {
                if ($sub_account_id == 0) {
                    $data = $data->where('type', 'client');
                } elseif ($sub_account_id == -1) {
                    $data = $data->where('type', 'supplier');
                } else {
                    $data = $data->where('sub_account', $sub_account_id);
                }
            }

            // Client filter
            if(!empty($client) && $client != '') {
                $data = $data->where('source_id', $client)->where('type', 'client');
            }

            // Supplier filter
            if(!empty($supplier) && $supplier != '') {
                $data = $data->where('source_id', $supplier)->where('type', 'supplier');
            }

            // Calculate totals for the filtered data
            // Clone the query to avoid affecting the DataTables query
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2)
                ])
                ->make();
        }

        return view('company.reports.account_statement',compact('balance','creditor','debtor','subAccounts','clients','suppliers'));
    }
    /**
     * @deprecated This method is deprecated. Use account_statement() with AJAX filters instead.
     */
    public function account_statement_filter(Request $request)
    {

        $company_id=Auth::guard('company')->user()->id;

        $clients = Client::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $suppliers = Supplier::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $balance=0;


        $item=AccountOperation::query()->where('company_id',$company_id);
        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }

        if(!is_null($request->sub_account_id)){

            if ($request->sub_account_id==0){

                $item=$item->where('type','client');


            }elseif ($request->sub_account_id==-1){

                $item=$item->where('type','supplier');

            }else{


                $item=$item->where('sub_account',$request->sub_account_id);

            }


        }


        if(!is_null($request->client)){
            $item=$item->where('source_id',$request->client)->where('type','client');
        }

        if(!is_null($request->supplier)){
            $item=$item->where('source_id',$request->supplier)->where('type','supplier');
        }



        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');





        return view('company.reports.account_statement_filter',compact('balance','creditor','debtor','data'));
    }

    public function clients_report()
    {
        $this->authorize('clients_report.index');
        $company_id=Auth::guard('company')->user()->id;

        $clients = Client::query()->where('company_id',$company_id)->get();
        $clientIds = Client::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $balance=0;
        $creditor=AccountOperation::query()->whereIn('source_id',$clientIds)->where('type','client')->sum('creditor');;
        $debtor=AccountOperation::query()->whereIn('source_id',$clientIds)->where('type','client')->sum('debtor');

        if (request()->ajax())
        {
            $data = AccountOperation::query()->whereIn('source_id',$clientIds)->where('type','client')->latest();

            // Apply all possible filters (unified approach)
            $search = request()->get('search', false);
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);
            $client = request()->get('client', false);

            // Search filter
            if(!empty($search) && $search != '') {
                $data = $data->where('details', 'like', '%' . $search. '%');
            }

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Specific client filter
            if(!empty($client) && $client != '') {
                $data = $data->where('source_id', $client);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            $totalBalance = $totalDebtor - $totalCreditor; // المتبقي = مدين - دائن

            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2),
                    'totalBalance' => number_format($totalBalance, 2)
                ])
                ->make();
        }

        return view('company.reports.clients_report',compact('balance','creditor','debtor','clients'));
    }
    /**
     * @deprecated This method is deprecated. Use clients_report() with AJAX filters instead.
     */
    public function clients_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $clients = Client::query()->where('company_id',$company_id)->get();
        $clientIds = Client::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$clientIds)->where('type','client');
        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }



        if(!is_null($request->client)){
            $item=$item->where('source_id',$request->client)->where('type','client');

        }




        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.clients_report_filter',compact('balance','creditor','debtor','clients','data'));
    }

    public function suppliers_report()
    {
        $this->authorize('suppliers_report.index');
        $company_id=Auth::guard('company')->user()->id;

        $suppliers = Supplier::query()->where('company_id',$company_id)->get();
        $supplierIds = Supplier::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $balance=0;
        $creditor=AccountOperation::query()->whereIn('source_id',$supplierIds)->where('type','supplier')->sum('creditor');;
        $debtor=AccountOperation::query()->whereIn('source_id',$supplierIds)->where('type','supplier')->sum('debtor');

        if (request()->ajax())
        {
            $data = AccountOperation::query()->whereIn('source_id',$supplierIds)->where('type','supplier')->latest();

            // Apply all possible filters (unified approach)
            $search = request()->get('search', false);
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);
            $supplier = request()->get('supplier', false);

            // Search filter
            if(!empty($search) && $search != '') {
                $data = $data->where('details', 'like', '%' . $search. '%');
            }

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Specific supplier filter
            if(!empty($supplier) && $supplier != '') {
                $data = $data->where('source_id', $supplier);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2)
                ])
                ->make();
        }

        return view('company.reports.suppliers_report',compact('balance','creditor','debtor','suppliers'));
    }

    /**
     * @deprecated This method is deprecated. Use suppliers_report() with AJAX filters instead.
     */
    public function suppliers_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $suppliers = Supplier::query()->where('company_id',$company_id)->get();
        $supplierIds = Supplier::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$supplierIds)->where('type','supplier');
        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }



        if(!is_null($request->supplier)){
            $item=$item->where('source_id',$request->supplier)->where('type','supplier');

        }




        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.suppliers_report_filter',compact('balance','creditor','debtor','suppliers','data'));
    }


    public function sales_report()
    {
        $this->authorize('sales_report.index');
        $company_id=Auth::guard('company')->user()->id;

        $invoiceIds = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->pluck('id')->toArray();
        $resalesds = Resale::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $balance=0;
        $creditor=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','resale')->whereIn('source_id',$resalesds)->sum('creditor');;
        $debtor=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','resale')->whereIn('source_id',$resalesds)->sum('debtor');


        if (request()->ajax())
        {
            $data = AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','resale')->whereIn('source_id',$resalesds)->latest();

            // Apply all possible filters (unified approach)
            $search = request()->get('search', false);
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);

            // Search filter
            if(!empty($search) && $search != '') {
                $data = $data->where('details', 'like', '%' . $search. '%');
            }

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2)
                ])
                ->make();
        }

        return view('company.reports.sales_report',compact('balance','creditor','debtor'));
    }

    /**
     * @deprecated This method is deprecated. Use sales_report() with AJAX filters instead.
     */
    public function sales_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $invoiceIds = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->pluck('id')->toArray();
        $resaleds = Resale::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','resale')->whereIn('source_id',$resaleds);

        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }







        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.sales_report_filter',compact('balance','creditor','debtor','data'));
    }

    public function purchases_report()
    {

        $this->authorize('purchases_report.index');

        $company_id=Auth::guard('company')->user()->id;

        $invoiceIds = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->pluck('id')->toArray();
        $repurchaseds = Repurchase::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $arry_data=array_merge($invoiceIds,$repurchaseds);

        $balance=0;
        $creditor=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','repurchase')->whereIn('source_id',$repurchaseds)->sum('creditor');;
        $debtor=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','repurchase')->whereIn('source_id',$repurchaseds)->sum('debtor');

        if (request()->ajax())
        {
            $data = AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','repurchase')->whereIn('source_id',$repurchaseds)->latest();

            // Apply all possible filters (unified approach)
            $search = request()->get('search', false);
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);

            // Search filter
            if(!empty($search) && $search != '') {
                $data = $data->where('details', 'like', '%' . $search. '%');
            }

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2)
                ])
                ->make();
        }

        return view('company.reports.purchases_report',compact('balance','creditor','debtor'));
    }
    /**
     * @deprecated This method is deprecated. Use purchases_report() with AJAX filters instead.
     */
    public function purchases_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $invoiceIds = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->pluck('id')->toArray();
        $repurchaseds = Repurchase::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','Repurchase')->whereIn('source_id',$repurchaseds);

        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }







        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.purchases_report_filter',compact('balance','creditor','debtor','data'));
    }


    public function expenses_report()
    {
        $this->authorize('expenses_report.index');

        $company_id=Auth::guard('company')->user()->id;



        $balance=0;
        $creditor=AccountOperation::query()->where('company_id',$company_id)->where('type_receipts','expense')->sum('creditor');
        $debtor=AccountOperation::query()->where('company_id',$company_id)->where('type_receipts','expense')->sum('debtor');

        if (request()->ajax())
        {
            $data = AccountOperation::query()->where('company_id',$company_id)->where('type_receipts','expense')->latest();

            // Apply all possible filters (unified approach)
            $search = request()->get('search', false);
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);

            // Search filter
            if(!empty($search) && $search != '') {
                $data = $data->where('details', 'like', '%' . $search. '%');
            }

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2)
                ])
                ->make();
        }

        return view('company.reports.expenses_report',compact('balance','creditor','debtor'));
    }

    /**
     * @deprecated This method is deprecated. Use expenses_report() with AJAX filters instead.
     */
    public function expenses_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $expenseIds = Expense::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$expenseIds)->where('type','expenses');

        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }







        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.expenses_report_filter',compact('balance','creditor','debtor','data'));
    }


    public function products_report()
    {
        $this->authorize('products_report.index');
        $company_id=Auth::guard('company')->user()->id;



        if (request()->ajax())
        {

            $data = Product::query()->where('company_id',$company_id)->latest();
            $search = request()->get('search', false);
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);
            if(!empty($search) || $search != '') {
                $data = $data->where('name', 'like', '%' . $search. '%');
            }
            if(!empty($from_date) || $from_date != '') {
                $data = $data->where('created_at','>=',$from_date);
            }
            if(!empty($to_date) || $to_date != '') {
                $data = $data->where('created_at','<=',$to_date);
            }
            return DataTables::make($data)
                ->escapeColumns([])
                ->make();
        }

        return view('company.reports.products_report');
    }
    public function tax_report(Request $request){

        $this->authorize('tax_report.index');

        $company_id=Auth::guard('company')->user()->id;

        $expenses_tax_15 = Expense::query()->where('company_id',$company_id)->where('percentage_tax','tax_15')->sum('amount');
        $expenses_tax_15_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','tax_15')->sum('amount_tax');

        $expenses_no_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','no_tax')->sum('amount');
        $expenses_zero_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','zero_tax')->sum('amount');
        $expenses_free_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','free_tax')->sum('amount');


        ////////////////

        $resaleIds=Resale::query()->where('company_id',$company_id)->where('company_id',$company_id)->pluck('invoice_id')->toArray();

        $sales_tax_15 = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->sum('total_taxable');
        $sales_tax_15_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->sum('tax_amount');
        $sales_tax_15_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->whereIn('id',$resaleIds)->sum('total_taxable');


        $sales_no_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','no_tax')->sum('total_taxable');
        $sales_no_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','no_tax')->sum('tax_amount');
        $sales_no_tax_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','no_tax')->whereIn('id',$resaleIds)->sum('total_taxable');

        $sales_zero_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','zero_tax')->sum('total_taxable');
        $sales_zero_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','zero_tax')->sum('tax_amount');
        $sales_zero_tax_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','zero_tax')->whereIn('id',$resaleIds)->sum('total_taxable');


        $sales_free_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','free_tax')->sum('total_taxable');
        $sales_free_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','free_tax')->sum('tax_amount');
        $sales_free_tax_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','free_tax')->whereIn('id',$resaleIds)->sum('total_taxable');

        //////////////////////////


        $repurchaseIds=Repurchase::query()->where('company_id',$company_id)->where('company_id',$company_id)->pluck('invoice_id')->toArray();
        $repurchase_tax_15 = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->sum('total_taxable');
        $repurchase_tax_15_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->sum('tax_amount');
        $repurchase_tax_15_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->whereIn('id',$repurchaseIds)->sum('total_taxable');


        $repurchase_no_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','no_tax')->sum('total_taxable');
        $repurchase_no_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','no_tax')->sum('tax_amount');
        $repurchase_no_tax_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','no_tax')->whereIn('id',$repurchaseIds)->sum('total_taxable');

        $repurchase_zero_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','zero_tax')->sum('total_taxable');
        $repurchase_zero_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','zero_tax')->sum('tax_amount');
        $repurchase_zero_tax_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','zero_tax')->whereIn('id',$repurchaseIds)->sum('total_taxable');


        $repurchase_free_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','free_tax')->sum('total_taxable');
        $repurchase_free_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','free_tax')->sum('tax_amount');
        $repurchase_free_tax_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','free_tax')->whereIn('id',$repurchaseIds)->sum('total_taxable');



        return view('company.reports.tax_report',compact('sales_free_tax_resales','sales_zero_tax_resales','sales_tax_15_resales','sales_no_tax_resales','sales_free_tax','sales_free_tax_amount','sales_zero_tax','sales_zero_tax_amount','sales_no_tax','sales_no_tax_amount','sales_tax_15','sales_tax_15_amount',
            'repurchase_tax_15','repurchase_tax_15_amount','repurchase_tax_15_repurchase','repurchase_no_tax','repurchase_no_tax_amount','repurchase_no_tax_repurchase','repurchase_zero_tax','repurchase_zero_tax_amount','repurchase_zero_tax_repurchase','repurchase_free_tax','repurchase_free_tax_amount','repurchase_free_tax_repurchase',
            'expenses_tax_15','expenses_tax_15_tax','expenses_no_tax','expenses_zero_tax','expenses_free_tax'));


    }
    public function trial_balance(Request $request){
        $this->authorize('trial_balance.index');

        $company_id=Auth::guard('company')->user()->id;


        if(!is_null($request->from_date)) {

            return $request->from_date;
        }
         /////

        $client_creditor=AccountOperation::query()->where('company_id',$company_id)->where('type','client')->sum('creditor');
        $client_debtor=AccountOperation::query()->where('company_id',$company_id)->where('type','client')->sum('debtor');
          /////
        $supplier_creditor=AccountOperation::query()->where('company_id',$company_id)->where('type','supplier')->sum('creditor');;
        $supplier_debtor=AccountOperation::query()->where('company_id',$company_id)->where('type','supplier')->sum('debtor');

          /////
        $mains=SubAccount::query()->where('company_id',$company_id)->get();



        $creditor=$client_creditor+$supplier_creditor;
        $debtor=$supplier_debtor+$client_debtor;


        $net_creditor=0;
        $net_debtor=0;

        if ($client_debtor < $client_creditor){

            $net_creditor=$net_creditor+($client_creditor-$client_debtor);


        }else{

            $net_debtor=$net_debtor+($client_debtor-$client_creditor);

        }

        if ($supplier_debtor < $supplier_creditor){

            $net_creditor=$net_creditor+($supplier_creditor-$supplier_debtor);

        }else{

            $net_debtor=$net_debtor+($supplier_debtor-$supplier_creditor);

        }

        foreach ($mains as $main){


                $creditor = $creditor + $main->creditor;
                $debtor = $debtor + $main->debtor;

            if ($main->debtor < $main->creditor){

                $net_creditor=$net_creditor+($main->creditor-$main->debtor);

            }else{

                $net_debtor=$net_debtor+($main->debtor-$main->creditor);


            }



        }




        return view('company.reports.trial_balance',compact('mains','debtor','creditor','client_creditor','client_debtor','supplier_creditor','supplier_debtor','net_creditor','net_debtor'));


    }
    public function balances(Request $request){
        $this->authorize('balances.index');
        $company_id=Auth::guard('company')->user()->id;

        ////


        $sub_account_sale=SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();
        $sales = AccountOperation::query()->where('sub_account', $sub_account_sale->id);
        $resale = AccountOperation::query()->where('sub_account', $sub_account_sale->id);

        $sub_account_purchase=SubAccount::query()->where('type_account','purchase')->where('company_id',$company_id)->first();
        $purchases = AccountOperation::query()->where('sub_account', $sub_account_purchase->id);
        $repurchase = AccountOperation::query()->where('sub_account', $sub_account_purchase->id);




        $expenses = Expense::query()->where('company_id',$company_id);



        $sales=$sales->sum('creditor');
        $purchases=$purchases->sum('debtor');


        $resale=$resale->sum('debtor');
        $repurchase=$repurchase->sum('creditor');

        $expenses=$expenses->sum('amount');

        $net_sale=$sales;
        $net_purchases=$purchases;

        $net=($net_sale+$repurchase)-($net_purchases+$expenses+$resale);

        ////

        $mainBasicIds=MainAccount::query()->where('parent_id',1)->pluck('id')->toArray();
        $basics=SubAccount::query()->whereIn('parent_id',$mainBasicIds)->where('company_id',$company_id)->get();

        $client_creditor=AccountOperation::query()->where('company_id',$company_id)->where('type','client')->sum('creditor');
        $client_debtor=AccountOperation::query()->where('company_id',$company_id)->where('type','client')->sum('debtor');

        $net_client=$client_debtor-$client_creditor;
        $sum_basics=$net_client;
        foreach ($basics as $basic){

             $sum_basics=$sum_basics + $basic->net_balance;

         }



        $expenses_tax_15_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','tax_15')->sum('amount_tax');

        ////////////////

        $resaleIds=Resale::query()->where('company_id',$company_id)->where('company_id',$company_id)->pluck('invoice_id')->toArray();

        $sales_tax_15 = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->sum('total_taxable');
        $sales_tax_15_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->whereIn('id',$resaleIds)->sum('total_taxable');


        //////////////////////////


        $repurchaseIds=Repurchase::query()->where('company_id',$company_id)->where('company_id',$company_id)->pluck('invoice_id')->toArray();
        $repurchase_tax_15 = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->sum('total_taxable');
        $repurchase_tax_15_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->whereIn('id',$repurchaseIds)->sum('total_taxable');

        $net_tax=(($sales_tax_15-$sales_tax_15_resales)*0.15)-((($repurchase_tax_15-$repurchase_tax_15_repurchase)*0.15)+$expenses_tax_15_tax);



        $mainObligationsIds=MainAccount::query()->where('parent_id',2)->pluck('id')->toArray();
        $obligations=SubAccount::query()->whereIn('parent_id',$mainObligationsIds)->where('company_id',$company_id)->get();

        $supplier_creditor=AccountOperation::query()->where('company_id',$company_id)->where('type','supplier')->sum('creditor');;
        $supplier_debtor=AccountOperation::query()->where('company_id',$company_id)->where('type','supplier')->sum('debtor');
        $net_supplier=$supplier_creditor-$supplier_debtor;
        $sum_obligations=$net_supplier+$net_tax;
        foreach ($obligations as $obligation){

            $sum_obligations=$sum_obligations + $obligation->net_balance;

        }

        $sum_copyright=0;
        $mainCopyrightsIds=MainAccount::query()->where('parent_id',3)->pluck('id')->toArray();
        $copyrights=SubAccount::query()->whereIn('parent_id',$mainCopyrightsIds)->where('company_id',$company_id)->get();
        foreach ($copyrights as $copyright){

            $sum_copyright=$sum_copyright + $copyright->net_balance;

        }




        return view('company.reports.balances',compact('net_supplier','net_tax','net_client','basics','sum_basics','obligations','sum_obligations','copyrights','sum_copyright','net'));


    }



}
