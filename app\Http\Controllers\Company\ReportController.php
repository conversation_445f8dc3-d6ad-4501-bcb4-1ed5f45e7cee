<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\AccountOperation;
use App\Models\Client;
use App\Models\Expense;
use App\Models\Invoice;
use App\Models\MainAccount;
use App\Models\Product;
use App\Models\Resale;
use App\Models\Repurchase;
use App\Models\SubAccount;
use App\Models\Supplier;
use Yajra\DataTables\DataTables;
use Auth;
use Illuminate\Http\Request;



class ReportController extends Controller
{



    public function income_list_report(Request $request){

        $this->authorize('income_list_report.index');

        $company_id=Auth::guard('company')->user()->id;

        // Initialize default values
        $net_sale = 0;
        $net_purchases = 0;
        $resale = 0;
        $repurchase = 0;
        $expenses = 0;
        $net = 0;

        // Get sale sub account
        $sub_account_sale = SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();
        if ($sub_account_sale) {
            $sales = AccountOperation::query()->where('sub_account', $sub_account_sale->id);
            $resale_query = AccountOperation::query()->where('sub_account', $sub_account_sale->id);
        } else {
            $sales = AccountOperation::query()->where('id', 0); // Empty query
            $resale_query = AccountOperation::query()->where('id', 0); // Empty query
        }

        // Get purchase sub account
        $sub_account_purchase = SubAccount::query()->where('type_account','purchase')->where('company_id',$company_id)->first();
        if ($sub_account_purchase) {
            $purchases = AccountOperation::query()->where('sub_account', $sub_account_purchase->id);
            $repurchase_query = AccountOperation::query()->where('sub_account', $sub_account_purchase->id);
        } else {
            $purchases = AccountOperation::query()->where('id', 0); // Empty query
            $repurchase_query = AccountOperation::query()->where('id', 0); // Empty query
        }

        $expenses_query = Expense::query()->where('company_id',$company_id);

        if(!is_null($request->from_date)) {
            if ($sub_account_sale) {
                $sales = $sales->whereDate('date','>=',$request->from_date);
                $resale_query = $resale_query->whereDate('date','>=',$request->from_date);
            }
            if ($sub_account_purchase) {
                $purchases = $purchases->whereDate('date','>=',$request->from_date);
                $repurchase_query = $repurchase_query->whereDate('date','>=',$request->from_date);
            }
            $expenses_query = $expenses_query->whereDate('date','>=',$request->from_date);
        }
        
        if(!is_null($request->to_date)) {
            if ($sub_account_sale) {
                $sales = $sales->whereDate('date','<=',$request->to_date);
                $resale_query = $resale_query->whereDate('date','<=',$request->to_date);
            }
            if ($sub_account_purchase) {
                $purchases = $purchases->whereDate('date','<=',$request->to_date);
                $repurchase_query = $repurchase_query->whereDate('date','<=',$request->to_date);
            }
            $expenses_query = $expenses_query->whereDate('date','<=',$request->to_date);
        }

        if (!is_null($request->from_date) && !is_null($request->to_date) ){
            if ($sub_account_sale) {
                $sales = $sales->whereBetween('date',[$request->from_date,$request->to_date]);
                $resale_query = $resale_query->whereBetween('date',[$request->from_date,$request->to_date]);
            }
            if ($sub_account_purchase) {
                $purchases = $purchases->whereBetween('date',[$request->from_date,$request->to_date]);
                $repurchase_query = $repurchase_query->whereBetween('date',[$request->from_date,$request->to_date]);
            }
            $expenses_query = $expenses_query->whereBetween('date',[$request->from_date,$request->to_date]);
        }

        // Calculate sums only if data exists
        if ($sub_account_sale) {
            $net_sale = $sales->sum('creditor');
            $resale = $resale_query->sum('debtor');
        }
        
        if ($sub_account_purchase) {
            $net_purchases = $purchases->sum('debtor');
            $repurchase = $repurchase_query->sum('creditor');
        }
        
        $expenses = $expenses_query->sum('amount');

        $net = ($net_sale + $repurchase) - ($net_purchases + $expenses + $resale);

        return view('company.reports.income_list_report',compact('net_sale','net_purchases','resale','repurchase','expenses','net'));


    }


    public function account_statement()
    {
        $this->authorize('account_statement.index');

        $company_id=Auth::guard('company')->user()->id;
//
        $clients = Client::query()->where('company_id',$company_id)->get();
        $suppliers = Supplier::query()->where('company_id',$company_id)->get();
//
//
//        $invoiceIds = Invoice::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $clientIds = Client::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $supplierIds = Supplier::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $expenseIds = Expense::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $resaleds = Resale::query()->where('company_id',$company_id)->pluck('id')->toArray();
//        $repurchaseds = Repurchase::query()->where('company_id',$company_id)->pluck('id')->toArray();
          $subAccounts = SubAccount::query()->where('company_id',$company_id)->get();
//
//
//
//
//        $arry_data=array_merge($invoiceIds,$clientIds,$supplierIds,$expenseIds,$resaleds,$repurchaseds,$subAccounts);

        $balance=0;
        $creditor=AccountOperation::query()->where('company_id',$company_id)->sum('creditor');;
        $debtor=AccountOperation::query()->where('company_id',$company_id)->sum('debtor');


        if (request()->ajax())
        {

            $data = AccountOperation::query()->where('company_id',$company_id)->latest();

            // Apply all possible filters (unified approach)
            $search = request()->get('search', false);
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);
            $sub_account_id = request()->get('sub_account_id', false);
            $client = request()->get('client', false);
            $supplier = request()->get('supplier', false);

            // Search filter
            if(!empty($search) && $search != '') {
                $data = $data->where('details', 'like', '%' . $search. '%');
            }

            // Date filters (improved logic)
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Sub account filters (from account_statement_filter logic)
            if(!empty($sub_account_id) && $sub_account_id != '') {
                if ($sub_account_id == 0) {
                    $data = $data->where('type', 'client');
                } elseif ($sub_account_id == -1) {
                    $data = $data->where('type', 'supplier');
                } else {
                    $data = $data->where('sub_account', $sub_account_id);
                }
            }

            // Client filter
            if(!empty($client) && $client != '') {
                $data = $data->where('source_id', $client)->where('type', 'client');
            }

            // Supplier filter
            if(!empty($supplier) && $supplier != '') {
                $data = $data->where('source_id', $supplier)->where('type', 'supplier');
            }

            // Calculate totals for the filtered data
            // Clone the query to avoid affecting the DataTables query
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2)
                ])
                ->make();
        }

        return view('company.reports.account_statement',compact('balance','creditor','debtor','subAccounts','clients','suppliers'));
    }
    /**
     * @deprecated This method is deprecated. Use account_statement() with AJAX filters instead.
     */
    public function account_statement_filter(Request $request)
    {

        $company_id=Auth::guard('company')->user()->id;

        $clients = Client::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $suppliers = Supplier::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $balance=0;


        $item=AccountOperation::query()->where('company_id',$company_id);
        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }

        if(!is_null($request->sub_account_id)){

            if ($request->sub_account_id==0){

                $item=$item->where('type','client');


            }elseif ($request->sub_account_id==-1){

                $item=$item->where('type','supplier');

            }else{


                $item=$item->where('sub_account',$request->sub_account_id);

            }


        }


        if(!is_null($request->client)){
            $item=$item->where('source_id',$request->client)->where('type','client');
        }

        if(!is_null($request->supplier)){
            $item=$item->where('source_id',$request->supplier)->where('type','supplier');
        }



        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');





        return view('company.reports.account_statement_filter',compact('balance','creditor','debtor','data'));
    }

    public function clients_report()
    {
        $this->authorize('clients_report.index');
        $company_id=Auth::guard('company')->user()->id;

        $clients = Client::query()->where('company_id',$company_id)->get();
        $clientIds = Client::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $balance=0;
        $creditor=AccountOperation::query()->whereIn('source_id',$clientIds)->where('type','client')->sum('creditor');;
        $debtor=AccountOperation::query()->whereIn('source_id',$clientIds)->where('type','client')->sum('debtor');

        if (request()->ajax())
        {
            $data = AccountOperation::query()->whereIn('source_id',$clientIds)->where('type','client')->latest();

            // Apply all possible filters (unified approach)
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);
            $client = request()->get('client', false);

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Specific client filter
            if(!empty($client) && $client != '') {
                $data = $data->where('source_id', $client);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            $totalBalance = $totalDebtor - $totalCreditor; // المتبقي = مدين - دائن

            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2),
                    'totalBalance' => number_format($totalBalance, 2)
                ])
                ->make();
        }

        return view('company.reports.clients_report',compact('balance','creditor','debtor','clients'));
    }
    /**
     * @deprecated This method is deprecated. Use clients_report() with AJAX filters instead.
     */
    public function clients_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $clients = Client::query()->where('company_id',$company_id)->get();
        $clientIds = Client::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$clientIds)->where('type','client');
        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }



        if(!is_null($request->client)){
            $item=$item->where('source_id',$request->client)->where('type','client');

        }




        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.clients_report_filter',compact('balance','creditor','debtor','clients','data'));
    }

    public function suppliers_report()
    {
        $this->authorize('suppliers_report.index');
        $company_id=Auth::guard('company')->user()->id;

        $suppliers = Supplier::query()->where('company_id',$company_id)->get();
        $supplierIds = Supplier::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $balance=0;
        $creditor=AccountOperation::query()->whereIn('source_id',$supplierIds)->where('type','supplier')->sum('creditor');;
        $debtor=AccountOperation::query()->whereIn('source_id',$supplierIds)->where('type','supplier')->sum('debtor');

        if (request()->ajax())
        {
            $data = AccountOperation::query()->whereIn('source_id',$supplierIds)->where('type','supplier')->latest();

            // Apply all possible filters (unified approach)
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);
            $supplier = request()->get('supplier', false);

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Specific supplier filter
            if(!empty($supplier) && $supplier != '') {
                $data = $data->where('source_id', $supplier);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2)
                ])
                ->make();
        }

        return view('company.reports.suppliers_report',compact('balance','creditor','debtor','suppliers'));
    }

    /**
     * @deprecated This method is deprecated. Use suppliers_report() with AJAX filters instead.
     */
    public function suppliers_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $suppliers = Supplier::query()->where('company_id',$company_id)->get();
        $supplierIds = Supplier::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$supplierIds)->where('type','supplier');
        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }



        if(!is_null($request->supplier)){
            $item=$item->where('source_id',$request->supplier)->where('type','supplier');

        }




        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.suppliers_report_filter',compact('balance','creditor','debtor','suppliers','data'));
    }


    public function sales_report()
    {
        $this->authorize('sales_report.index');
        $company_id=Auth::guard('company')->user()->id;

        $invoiceIds = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->pluck('id')->toArray();
        $resalesds = Resale::query()->where('company_id',$company_id)->pluck('id')->toArray();

        // Debug: Check if we have any invoice or resale IDs
        \Log::info('Sales Report Base Data', [
            'company_id' => $company_id,
            'invoice_count' => count($invoiceIds),
            'resale_count' => count($resalesds),
            'invoice_ids' => $invoiceIds,
            'resale_ids' => $resalesds
        ]);

        $balance=0;
        $creditor=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','resale')->whereIn('source_id',$resalesds)->sum('creditor');
        $debtor=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','resale')->whereIn('source_id',$resalesds)->sum('debtor');


        if (request()->ajax())
        {
            // Create base query with proper OR grouping
            $data = AccountOperation::query()
                ->where(function($query) use ($invoiceIds, $resalesds) {
                    $query->where(function($q) use ($invoiceIds) {
                        $q->whereIn('source_id', $invoiceIds)->where('type', 'invoice');
                    })
                    ->orWhere(function($q) use ($resalesds) {
                        $q->whereIn('source_id', $resalesds)->where('type', 'resale');
                    });
                })
                ->latest();

            // Apply all possible filters (unified approach)
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            $recordCount = $totalsQuery->count();

            // Debug logging
            \Log::info('Sales Report Debug', [
                'record_count' => $recordCount,
                'total_creditor' => $totalCreditor,
                'total_debtor' => $totalDebtor,
                'filters' => [
                    'from_date' => $from_date,
                    'to_date' => $to_date
                ]
            ]);

            // Calculate filtered balance (you can modify this logic as needed)
            $filteredBalance = $totalDebtor - $totalCreditor; // or whatever balance calculation you need

            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2),
                    'totalBalance' => number_format($filteredBalance, 2),
                    'recordCount' => $recordCount
                ])
                ->make();
        }

        return view('company.reports.sales_report',compact('balance','creditor','debtor'));
    }

    /**
     * @deprecated This method is deprecated. Use sales_report() with AJAX filters instead.
     */
    public function sales_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $invoiceIds = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->pluck('id')->toArray();
        $resaleds = Resale::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','resale')->whereIn('source_id',$resaleds);

        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }







        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.sales_report_filter',compact('balance','creditor','debtor','data'));
    }

    public function purchases_report()
    {

        $this->authorize('purchases_report.index');

        $company_id=Auth::guard('company')->user()->id;

        $invoiceIds = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->pluck('id')->toArray();
        $repurchaseds = Repurchase::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $arry_data=array_merge($invoiceIds,$repurchaseds);

        $balance=0;
        $creditor = AccountOperation::query()
            ->where(function($query) use ($invoiceIds, $repurchaseds) {
                $query->where(function($q) use ($invoiceIds) {
                    $q->whereIn('source_id', $invoiceIds)->where('type', 'invoice');
                })->orWhere(function($q) use ($repurchaseds) {
                    $q->whereIn('source_id', $repurchaseds)->where('type', 'repurchase');
                });
            })
            ->sum('creditor');

        $debtor = AccountOperation::query()
            ->where(function($query) use ($invoiceIds, $repurchaseds) {
                $query->where(function($q) use ($invoiceIds) {
                    $q->whereIn('source_id', $invoiceIds)->where('type', 'invoice');
                })->orWhere(function($q) use ($repurchaseds) {
                    $q->whereIn('source_id', $repurchaseds)->where('type', 'repurchase');
                });
            })
            ->sum('debtor');

        if (request()->ajax())
        {
            // Create base query with proper OR grouping
            $data = AccountOperation::query()
                ->where(function($query) use ($invoiceIds, $repurchaseds) {
                    $query->where(function($q) use ($invoiceIds) {
                        $q->whereIn('source_id', $invoiceIds)->where('type', 'invoice');
                    })
                    ->orWhere(function($q) use ($repurchaseds) {
                        $q->whereIn('source_id', $repurchaseds)->where('type', 'repurchase');
                    });
                })
                ->latest();

            // Apply all possible filters (unified approach)
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2)
                ])
                ->make();
        }

        return view('company.reports.purchases_report',compact('balance','creditor','debtor'));
    }
    /**
     * @deprecated This method is deprecated. Use purchases_report() with AJAX filters instead.
     */
    public function purchases_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $invoiceIds = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->pluck('id')->toArray();
        $repurchaseds = Repurchase::query()->where('company_id',$company_id)->pluck('id')->toArray();
        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$invoiceIds)->where('type','invoice')->orwhere('type','Repurchase')->whereIn('source_id',$repurchaseds);

        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }







        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.purchases_report_filter',compact('balance','creditor','debtor','data'));
    }


    public function expenses_report()
    {
        $this->authorize('expenses_report.index');

        $company_id=Auth::guard('company')->user()->id;



        $balance=0;
        $creditor=AccountOperation::query()->where('company_id',$company_id)->where('type_receipts','expense')->sum('creditor');
        $debtor=AccountOperation::query()->where('company_id',$company_id)->where('type_receipts','expense')->sum('debtor');

        if (request()->ajax())
        {
            $data = AccountOperation::query()->where('company_id',$company_id)->where('type_receipts','expense')->latest();

            // Apply all possible filters (unified approach)
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);

            // Date filters
            if(!empty($from_date) && $from_date != '') {
                $data = $data->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $data = $data->whereDate('date', '<=', $to_date);
            }

            // Calculate totals for the filtered data
            $totalsQuery = clone $data;
            $totalCreditor = $totalsQuery->sum('creditor');
            $totalDebtor = $totalsQuery->sum('debtor');
            return DataTables::make($data)
                ->escapeColumns([])
                ->with([
                    'totalCreditor' => number_format($totalCreditor, 2),
                    'totalDebtor' => number_format($totalDebtor, 2)
                ])
                ->make();
        }

        return view('company.reports.expenses_report',compact('balance','creditor','debtor'));
    }

    /**
     * @deprecated This method is deprecated. Use expenses_report() with AJAX filters instead.
     */
    public function expenses_report_filter(Request $request)
    {


        $company_id=Auth::guard('company')->user()->id;
        $expenseIds = Expense::query()->where('company_id',$company_id)->pluck('id')->toArray();

        $balance=0;


        $item=AccountOperation::query()->whereIn('source_id',$expenseIds)->where('type','expenses');

        if(!is_null($request->from_date))
        {

            $item=$item->whereDate('date','>=',$request->from_date);


        }

        if(!is_null($request->to_date)){

            $item=$item->where('date','<=',$request->to_date);
        }


        if (!is_null($request->from_date) && !is_null($request->to_date) ){

            $item=$item->whereBetween('date',[$request->from_date,$request->to_date]);
        }







        $data=$item->get();

        $creditor=$data->sum('creditor');
        $debtor=$data->sum('debtor');

        return view('company.reports.expenses_report_filter',compact('balance','creditor','debtor','data'));
    }


    public function products_report()
    {
        $this->authorize('products_report.index');
        $company_id=Auth::guard('company')->user()->id;



        if (request()->ajax())
        {

            $data = Product::query()->where('company_id',$company_id)->latest();
            $search = request()->get('search', false);
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);
            if(!empty($search) || $search != '') {
                $data = $data->where('name', 'like', '%' . $search. '%');
            }
            if(!empty($from_date) || $from_date != '') {
                $data = $data->where('created_at','>=',$from_date);
            }
            if(!empty($to_date) || $to_date != '') {
                $data = $data->where('created_at','<=',$to_date);
            }
            return DataTables::make($data)
                ->escapeColumns([])
                ->make();
        }

        return view('company.reports.products_report');
    }
    public function tax_report(Request $request){

        $this->authorize('tax_report.index');

        $company_id=Auth::guard('company')->user()->id;

        // Base calculations for display cards (unfiltered)
        $expenses_tax_15 = Expense::query()->where('company_id',$company_id)->where('percentage_tax','tax_15')->sum('amount');
        $expenses_tax_15_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','tax_15')->sum('amount_tax');

        $expenses_no_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','no_tax')->sum('amount');
        $expenses_zero_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','zero_tax')->sum('amount');
        $expenses_free_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','free_tax')->sum('amount');


        ////////////////

        $resaleIds=Resale::query()->where('company_id',$company_id)->where('company_id',$company_id)->pluck('invoice_id')->toArray();

        $sales_tax_15 = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->sum('total_taxable');
        $sales_tax_15_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->sum('tax_amount');
        $sales_tax_15_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->whereIn('id',$resaleIds)->sum('total_taxable');


        $sales_no_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','no_tax')->sum('total_taxable');
        $sales_no_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','no_tax')->sum('tax_amount');
        $sales_no_tax_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','no_tax')->whereIn('id',$resaleIds)->sum('total_taxable');

        $sales_zero_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','zero_tax')->sum('total_taxable');
        $sales_zero_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','zero_tax')->sum('tax_amount');
        $sales_zero_tax_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','zero_tax')->whereIn('id',$resaleIds)->sum('total_taxable');


        $sales_free_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','free_tax')->sum('total_taxable');
        $sales_free_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','free_tax')->sum('tax_amount');
        $sales_free_tax_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','free_tax')->whereIn('id',$resaleIds)->sum('total_taxable');

        //////////////////////////


        $repurchaseIds=Repurchase::query()->where('company_id',$company_id)->where('company_id',$company_id)->pluck('invoice_id')->toArray();
        $repurchase_tax_15 = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->sum('total_taxable');
        $repurchase_tax_15_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->sum('tax_amount');
        $repurchase_tax_15_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->whereIn('id',$repurchaseIds)->sum('total_taxable');


        $repurchase_no_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','no_tax')->sum('total_taxable');
        $repurchase_no_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','no_tax')->sum('tax_amount');
        $repurchase_no_tax_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','no_tax')->whereIn('id',$repurchaseIds)->sum('total_taxable');

        $repurchase_zero_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','zero_tax')->sum('total_taxable');
        $repurchase_zero_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','zero_tax')->sum('tax_amount');
        $repurchase_zero_tax_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','zero_tax')->whereIn('id',$repurchaseIds)->sum('total_taxable');


        $repurchase_free_tax = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','free_tax')->sum('total_taxable');
        $repurchase_free_tax_amount = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','free_tax')->sum('tax_amount');
        $repurchase_free_tax_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','free_tax')->whereIn('id',$repurchaseIds)->sum('total_taxable');



        // Handle AJAX requests for filtered data
        if (request()->ajax()) {
            // Apply date filters to all calculations
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);

            // Apply date filters to expenses
            $expenses_query = Expense::query()->where('company_id',$company_id);
            if(!empty($from_date) && $from_date != '') {
                $expenses_query = $expenses_query->whereDate('created_at', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $expenses_query = $expenses_query->whereDate('created_at', '<=', $to_date);
            }

            // Apply date filters to invoices
            $invoice_query = Invoice::query()->where('company_id',$company_id);
            if(!empty($from_date) && $from_date != '') {
                $invoice_query = $invoice_query->whereDate('created_at', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $invoice_query = $invoice_query->whereDate('created_at', '<=', $to_date);
            }

            // Get resale and repurchase IDs for filtered calculations
            $resaleIds = Resale::query()->where('company_id',$company_id)->pluck('invoice_id')->toArray();
            $repurchaseIds = Repurchase::query()->where('company_id',$company_id)->pluck('invoice_id')->toArray();

            // Recalculate ALL filtered expenses
            $filtered_expenses_tax_15 = (clone $expenses_query)->where('percentage_tax','tax_15')->sum('amount');
            $filtered_expenses_tax_15_tax = (clone $expenses_query)->where('percentage_tax','tax_15')->sum('amount_tax');
            $filtered_expenses_no_tax = (clone $expenses_query)->where('percentage_tax','no_tax')->sum('amount');
            $filtered_expenses_zero_tax = (clone $expenses_query)->where('percentage_tax','zero_tax')->sum('amount');
            $filtered_expenses_free_tax = (clone $expenses_query)->where('percentage_tax','free_tax')->sum('amount');

            // Recalculate ALL filtered sales (including resales)
            $filtered_sales_tax_15 = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','tax_15')->sum('total_taxable');
            $filtered_sales_tax_15_resales = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','tax_15')->whereIn('id',$resaleIds)->sum('total_taxable');
            $filtered_sales_tax_15_amount = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','tax_15')->sum('tax_amount');

            $filtered_sales_no_tax = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','no_tax')->sum('total_taxable');
            $filtered_sales_no_tax_resales = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','no_tax')->whereIn('id',$resaleIds)->sum('total_taxable');
            $filtered_sales_no_tax_amount = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','no_tax')->sum('tax_amount');

            $filtered_sales_zero_tax = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','zero_tax')->sum('total_taxable');
            $filtered_sales_zero_tax_resales = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','zero_tax')->whereIn('id',$resaleIds)->sum('total_taxable');
            $filtered_sales_zero_tax_amount = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','zero_tax')->sum('tax_amount');

            $filtered_sales_free_tax = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','free_tax')->sum('total_taxable');
            $filtered_sales_free_tax_resales = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','free_tax')->whereIn('id',$resaleIds)->sum('total_taxable');
            $filtered_sales_free_tax_amount = (clone $invoice_query)->where('type_invoices','sales')->where('type_tax','free_tax')->sum('tax_amount');

            // Recalculate ALL filtered purchases (including repurchases)
            $filtered_repurchase_tax_15 = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','tax_15')->sum('total_taxable');
            $filtered_repurchase_tax_15_repurchase = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','tax_15')->whereIn('id',$repurchaseIds)->sum('total_taxable');
            $filtered_repurchase_tax_15_amount = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','tax_15')->sum('tax_amount');

            $filtered_repurchase_no_tax = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','no_tax')->sum('total_taxable');
            $filtered_repurchase_no_tax_repurchase = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','no_tax')->whereIn('id',$repurchaseIds)->sum('total_taxable');
            $filtered_repurchase_no_tax_amount = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','no_tax')->sum('tax_amount');

            $filtered_repurchase_zero_tax = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','zero_tax')->sum('total_taxable');
            $filtered_repurchase_zero_tax_repurchase = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','zero_tax')->whereIn('id',$repurchaseIds)->sum('total_taxable');
            $filtered_repurchase_zero_tax_amount = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','zero_tax')->sum('tax_amount');

            $filtered_repurchase_free_tax = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','free_tax')->sum('total_taxable');
            $filtered_repurchase_free_tax_repurchase = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','free_tax')->whereIn('id',$repurchaseIds)->sum('total_taxable');
            $filtered_repurchase_free_tax_amount = (clone $invoice_query)->where('type_invoices','purchases')->where('type_tax','free_tax')->sum('tax_amount');

            // Calculate totals and tax amounts
            $filtered_total_sales = $filtered_sales_tax_15 + $filtered_sales_no_tax + $filtered_sales_zero_tax + $filtered_sales_free_tax;
            $filtered_total_sales_resales = $filtered_sales_tax_15_resales + $filtered_sales_no_tax_resales + $filtered_sales_zero_tax_resales + $filtered_sales_free_tax_resales;
            $filtered_total_purchases = $filtered_repurchase_tax_15 + $filtered_repurchase_no_tax + $filtered_repurchase_zero_tax + $filtered_repurchase_free_tax;
            $filtered_total_purchases_repurchase = $filtered_repurchase_tax_15_repurchase + $filtered_repurchase_no_tax_repurchase + $filtered_repurchase_zero_tax_repurchase + $filtered_repurchase_free_tax_repurchase;

            // Calculate total tax amounts for the totals rows
            $filtered_total_sales_tax = ($filtered_sales_tax_15 - $filtered_sales_tax_15_resales) * 0.15; // Only net 15% tax items contribute to tax
            $filtered_total_purchases_tax = ($filtered_repurchase_tax_15 - $filtered_repurchase_tax_15_repurchase) * 0.15; // Only net 15% tax items contribute to tax

            $filtered_final_tax = $filtered_total_sales_tax - ($filtered_total_purchases_tax + $filtered_expenses_tax_15_tax);

            return response()->json([
                // Expenses
                'expensesTax15' => number_format($filtered_expenses_tax_15, 2),
                'expensesTax15Tax' => number_format($filtered_expenses_tax_15_tax, 2),
                'expensesNoTax' => number_format($filtered_expenses_no_tax, 2),
                'expensesZeroTax' => number_format($filtered_expenses_zero_tax, 2),
                'expensesFreeTax' => number_format($filtered_expenses_free_tax, 2),

                // Sales
                'salesTax15' => number_format($filtered_sales_tax_15, 2),
                'salesTax15Resales' => number_format($filtered_sales_tax_15_resales, 2),
                'salesTax15Amount' => number_format($filtered_sales_tax_15_amount, 2),
                'salesNoTax' => number_format($filtered_sales_no_tax, 2),
                'salesNoTaxResales' => number_format($filtered_sales_no_tax_resales, 2),
                'salesNoTaxAmount' => number_format($filtered_sales_no_tax_amount, 2),
                'salesZeroTax' => number_format($filtered_sales_zero_tax, 2),
                'salesZeroTaxResales' => number_format($filtered_sales_zero_tax_resales, 2),
                'salesZeroTaxAmount' => number_format($filtered_sales_zero_tax_amount, 2),
                'salesFreeTax' => number_format($filtered_sales_free_tax, 2),
                'salesFreeTaxResales' => number_format($filtered_sales_free_tax_resales, 2),
                'salesFreeTaxAmount' => number_format($filtered_sales_free_tax_amount, 2),

                // Purchases
                'purchasesTax15' => number_format($filtered_repurchase_tax_15, 2),
                'purchasesTax15Repurchase' => number_format($filtered_repurchase_tax_15_repurchase, 2),
                'purchasesTax15Amount' => number_format($filtered_repurchase_tax_15_amount, 2),
                'purchasesNoTax' => number_format($filtered_repurchase_no_tax, 2),
                'purchasesNoTaxRepurchase' => number_format($filtered_repurchase_no_tax_repurchase, 2),
                'purchasesNoTaxAmount' => number_format($filtered_repurchase_no_tax_amount, 2),
                'purchasesZeroTax' => number_format($filtered_repurchase_zero_tax, 2),
                'purchasesZeroTaxRepurchase' => number_format($filtered_repurchase_zero_tax_repurchase, 2),
                'purchasesZeroTaxAmount' => number_format($filtered_repurchase_zero_tax_amount, 2),
                'purchasesFreeTax' => number_format($filtered_repurchase_free_tax, 2),
                'purchasesFreeRepurchase' => number_format($filtered_repurchase_free_tax_repurchase, 2),
                'purchasesFreeTaxAmount' => number_format($filtered_repurchase_free_tax_amount, 2),

                // Totals
                'totalSales' => number_format($filtered_total_sales, 2),
                'totalSalesResales' => number_format($filtered_total_sales_resales, 2),
                'totalPurchases' => number_format($filtered_total_purchases, 2),
                'totalPurchasesRepurchase' => number_format($filtered_total_purchases_repurchase, 2),
                'totalSalesTax' => number_format($filtered_total_sales_tax, 2),
                'totalPurchasesTax' => number_format($filtered_total_purchases_tax, 2),
                'finalTax' => number_format($filtered_final_tax, 2),
            ]);
        }

        return view('company.reports.tax_report',compact('sales_free_tax_resales','sales_zero_tax_resales','sales_tax_15_resales','sales_no_tax_resales','sales_free_tax','sales_free_tax_amount','sales_zero_tax','sales_zero_tax_amount','sales_no_tax','sales_no_tax_amount','sales_tax_15','sales_tax_15_amount',
            'repurchase_tax_15','repurchase_tax_15_amount','repurchase_tax_15_repurchase','repurchase_no_tax','repurchase_no_tax_amount','repurchase_no_tax_repurchase','repurchase_zero_tax','repurchase_zero_tax_amount','repurchase_zero_tax_repurchase','repurchase_free_tax','repurchase_free_tax_amount','repurchase_free_tax_repurchase',
            'expenses_tax_15','expenses_tax_15_tax','expenses_no_tax','expenses_zero_tax','expenses_free_tax'));


    }
    public function trial_balance(Request $request){
        $this->authorize('trial_balance.index');

        $company_id=Auth::guard('company')->user()->id;

        // Base calculations for display cards (unfiltered)
        $client_creditor=AccountOperation::query()->where('company_id',$company_id)->where('type','client')->sum('creditor');
        $client_debtor=AccountOperation::query()->where('company_id',$company_id)->where('type','client')->sum('debtor');
        $supplier_creditor=AccountOperation::query()->where('company_id',$company_id)->where('type','supplier')->sum('creditor');
        $supplier_debtor=AccountOperation::query()->where('company_id',$company_id)->where('type','supplier')->sum('debtor');

          /////
        $mains=SubAccount::query()->where('company_id',$company_id)->get();



        $creditor=$client_creditor+$supplier_creditor;
        $debtor=$supplier_debtor+$client_debtor;


        $net_creditor=0;
        $net_debtor=0;

        if ($client_debtor < $client_creditor){

            $net_creditor=$net_creditor+($client_creditor-$client_debtor);


        }else{

            $net_debtor=$net_debtor+($client_debtor-$client_creditor);

        }

        if ($supplier_debtor < $supplier_creditor){

            $net_creditor=$net_creditor+($supplier_creditor-$supplier_debtor);

        }else{

            $net_debtor=$net_debtor+($supplier_debtor-$supplier_creditor);

        }

        foreach ($mains as $main){


                $creditor = $creditor + $main->creditor;
                $debtor = $debtor + $main->debtor;

            if ($main->debtor < $main->creditor){

                $net_creditor=$net_creditor+($main->creditor-$main->debtor);

            }else{

                $net_debtor=$net_debtor+($main->debtor-$main->creditor);


            }



        }




        // Handle AJAX requests for filtered data
        if (request()->ajax()) {
            // Apply date filters
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);

            // Apply date filters to account operations
            $account_query = AccountOperation::query()->where('company_id',$company_id);
            if(!empty($from_date) && $from_date != '') {
                $account_query = $account_query->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $account_query = $account_query->whereDate('date', '<=', $to_date);
            }

            // Recalculate ALL filtered totals
            $filtered_client_creditor = (clone $account_query)->where('type','client')->sum('creditor');
            $filtered_client_debtor = (clone $account_query)->where('type','client')->sum('debtor');
            $filtered_supplier_creditor = (clone $account_query)->where('type','supplier')->sum('creditor');
            $filtered_supplier_debtor = (clone $account_query)->where('type','supplier')->sum('debtor');

            $filtered_creditor = $filtered_client_creditor + $filtered_supplier_creditor;
            $filtered_debtor = $filtered_client_debtor + $filtered_supplier_debtor;
            $filtered_net_creditor = $filtered_creditor - $filtered_debtor;
            $filtered_net_debtor = $filtered_debtor - $filtered_creditor;

            // Get filtered main accounts data
            $filtered_mains = MainAccount::query()->where('company_id',$company_id)->get();
            $filtered_main_accounts = [];

            foreach($filtered_mains as $main) {
                $main_creditor = (clone $account_query)->where('main_account_id', $main->id)->sum('creditor');
                $main_debtor = (clone $account_query)->where('main_account_id', $main->id)->sum('debtor');
                $main_net_creditor = $main_creditor - $main_debtor;
                $main_net_debtor = $main_debtor - $main_creditor;

                $filtered_main_accounts[] = [
                    'id' => $main->id,
                    'name' => $main->name,
                    'creditor' => number_format($main_creditor, 2),
                    'debtor' => number_format($main_debtor, 2),
                    'net_creditor' => number_format($main_net_creditor, 2),
                    'net_debtor' => number_format($main_net_debtor, 2),
                ];
            }

            return response()->json([
                'totalCreditor' => number_format($filtered_creditor, 2),
                'totalDebtor' => number_format($filtered_debtor, 2),
                'netCreditor' => number_format($filtered_net_creditor, 2),
                'netDebtor' => number_format($filtered_net_debtor, 2),
                'clientCreditor' => number_format($filtered_client_creditor, 2),
                'clientDebtor' => number_format($filtered_client_debtor, 2),
                'supplierCreditor' => number_format($filtered_supplier_creditor, 2),
                'supplierDebtor' => number_format($filtered_supplier_debtor, 2),
                'mainAccounts' => $filtered_main_accounts,
            ]);
        }

        return view('company.reports.trial_balance',compact('mains','debtor','creditor','client_creditor','client_debtor','supplier_creditor','supplier_debtor','net_creditor','net_debtor'));


    }
    public function balances(Request $request){
        $this->authorize('balances.index');
        $company_id=Auth::guard('company')->user()->id;

        ////


        $sub_account_sale=SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();
        $sales = AccountOperation::query()->where('sub_account', $sub_account_sale->id);
        $resale = AccountOperation::query()->where('sub_account', $sub_account_sale->id);

        $sub_account_purchase=SubAccount::query()->where('type_account','purchase')->where('company_id',$company_id)->first();
        $purchases = AccountOperation::query()->where('sub_account', $sub_account_purchase->id);
        $repurchase = AccountOperation::query()->where('sub_account', $sub_account_purchase->id);




        $expenses = Expense::query()->where('company_id',$company_id);



        $sales=$sales->sum('creditor');
        $purchases=$purchases->sum('debtor');


        $resale=$resale->sum('debtor');
        $repurchase=$repurchase->sum('creditor');

        $expenses=$expenses->sum('amount');

        $net_sale=$sales;
        $net_purchases=$purchases;

        $net=($net_sale+$repurchase)-($net_purchases+$expenses+$resale);

        ////

        $mainBasicIds=MainAccount::query()->where('parent_id',1)->pluck('id')->toArray();
        $basics=SubAccount::query()->whereIn('parent_id',$mainBasicIds)->where('company_id',$company_id)->get();

        $client_creditor=AccountOperation::query()->where('company_id',$company_id)->where('type','client')->sum('creditor');
        $client_debtor=AccountOperation::query()->where('company_id',$company_id)->where('type','client')->sum('debtor');

        $net_client=$client_debtor-$client_creditor;
        $sum_basics=$net_client;
        foreach ($basics as $basic){

             $sum_basics=$sum_basics + $basic->net_balance;

         }



        $expenses_tax_15_tax = Expense::query()->where('company_id',$company_id)->where('percentage_tax','tax_15')->sum('amount_tax');

        ////////////////

        $resaleIds=Resale::query()->where('company_id',$company_id)->where('company_id',$company_id)->pluck('invoice_id')->toArray();

        $sales_tax_15 = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->sum('total_taxable');
        $sales_tax_15_resales = Invoice::query()->where('company_id',$company_id)->where('type_invoices','sales')->where('type_tax','tax_15')->whereIn('id',$resaleIds)->sum('total_taxable');


        //////////////////////////


        $repurchaseIds=Repurchase::query()->where('company_id',$company_id)->where('company_id',$company_id)->pluck('invoice_id')->toArray();
        $repurchase_tax_15 = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->sum('total_taxable');
        $repurchase_tax_15_repurchase = Invoice::query()->where('company_id',$company_id)->where('type_invoices','purchases')->where('type_tax','tax_15')->whereIn('id',$repurchaseIds)->sum('total_taxable');

        $net_tax=(($sales_tax_15-$sales_tax_15_resales)*0.15)-((($repurchase_tax_15-$repurchase_tax_15_repurchase)*0.15)+$expenses_tax_15_tax);



        $mainObligationsIds=MainAccount::query()->where('parent_id',2)->pluck('id')->toArray();
        $obligations=SubAccount::query()->whereIn('parent_id',$mainObligationsIds)->where('company_id',$company_id)->get();

        $supplier_creditor=AccountOperation::query()->where('company_id',$company_id)->where('type','supplier')->sum('creditor');;
        $supplier_debtor=AccountOperation::query()->where('company_id',$company_id)->where('type','supplier')->sum('debtor');
        $net_supplier=$supplier_creditor-$supplier_debtor;
        $sum_obligations=$net_supplier+$net_tax;
        foreach ($obligations as $obligation){

            $sum_obligations=$sum_obligations + $obligation->net_balance;

        }

        $sum_copyright=0;
        $mainCopyrightsIds=MainAccount::query()->where('parent_id',3)->pluck('id')->toArray();
        $copyrights=SubAccount::query()->whereIn('parent_id',$mainCopyrightsIds)->where('company_id',$company_id)->get();
        foreach ($copyrights as $copyright){

            $sum_copyright=$sum_copyright + $copyright->net_balance;

        }




        // Handle AJAX requests for filtered data
        if (request()->ajax()) {
            // Apply date filters
            $from_date = request()->get('from_date', false);
            $to_date = request()->get('to_date', false);

            // Apply date filters to account operations
            $account_query = AccountOperation::query()->where('company_id',$company_id);
            if(!empty($from_date) && $from_date != '') {
                $account_query = $account_query->whereDate('date', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $account_query = $account_query->whereDate('date', '<=', $to_date);
            }

            // Apply date filters to expenses
            $expense_query = Expense::query()->where('company_id',$company_id);
            if(!empty($from_date) && $from_date != '') {
                $expense_query = $expense_query->whereDate('created_at', '>=', $from_date);
            }
            if(!empty($to_date) && $to_date != '') {
                $expense_query = $expense_query->whereDate('created_at', '<=', $to_date);
            }

            // Recalculate ALL filtered totals
            $filtered_client_creditor = (clone $account_query)->where('type','client')->sum('creditor');
            $filtered_client_debtor = (clone $account_query)->where('type','client')->sum('debtor');
            $filtered_supplier_creditor = (clone $account_query)->where('type','supplier')->sum('creditor');
            $filtered_supplier_debtor = (clone $account_query)->where('type','supplier')->sum('debtor');
            $filtered_expenses = $expense_query->sum('amount');

            $filtered_net_client = $filtered_client_debtor - $filtered_client_creditor;
            $filtered_net_supplier = $filtered_supplier_creditor - $filtered_supplier_debtor;
            $filtered_net_tax = $filtered_expenses * 0.15; // Assuming 15% tax rate

            // Calculate filtered basics (assets)
            $filtered_basics = BasicAccount::query()->where('company_id',$company_id)->get();
            $filtered_sum_basics = 0;
            $filtered_basic_accounts = [];

            foreach($filtered_basics as $basic) {
                $basic_creditor = (clone $account_query)->where('basic_account_id', $basic->id)->sum('creditor');
                $basic_debtor = (clone $account_query)->where('basic_account_id', $basic->id)->sum('debtor');
                $basic_net = $basic_debtor - $basic_creditor;
                $filtered_sum_basics += $basic_net;

                $filtered_basic_accounts[] = [
                    'id' => $basic->id,
                    'name' => $basic->name,
                    'net' => number_format($basic_net, 2),
                ];
            }

            // Calculate filtered obligations (liabilities)
            $filtered_obligations = ObligationAccount::query()->where('company_id',$company_id)->get();
            $filtered_sum_obligations = 0;
            $filtered_obligation_accounts = [];

            foreach($filtered_obligations as $obligation) {
                $obligation_creditor = (clone $account_query)->where('obligation_account_id', $obligation->id)->sum('creditor');
                $obligation_debtor = (clone $account_query)->where('obligation_account_id', $obligation->id)->sum('debtor');
                $obligation_net = $obligation_creditor - $obligation_debtor;
                $filtered_sum_obligations += $obligation_net;

                $filtered_obligation_accounts[] = [
                    'id' => $obligation->id,
                    'name' => $obligation->name,
                    'net' => number_format($obligation_net, 2),
                ];
            }

            // Calculate filtered copyrights (equity)
            $filtered_copyrights = CopyrightAccount::query()->where('company_id',$company_id)->get();
            $filtered_sum_copyright = 0;
            $filtered_copyright_accounts = [];

            foreach($filtered_copyrights as $copyright) {
                $copyright_creditor = (clone $account_query)->where('copyright_account_id', $copyright->id)->sum('creditor');
                $copyright_debtor = (clone $account_query)->where('copyright_account_id', $copyright->id)->sum('debtor');
                $copyright_net = $copyright_creditor - $copyright_debtor;
                $filtered_sum_copyright += $copyright_net;

                $filtered_copyright_accounts[] = [
                    'id' => $copyright->id,
                    'name' => $copyright->name,
                    'net' => number_format($copyright_net, 2),
                ];
            }

            $filtered_net = $filtered_sum_basics - ($filtered_sum_obligations + $filtered_sum_copyright);

            return response()->json([
                'netClient' => number_format($filtered_net_client, 2),
                'netSupplier' => number_format($filtered_net_supplier, 2),
                'netTax' => number_format($filtered_net_tax, 2),
                'sumBasics' => number_format($filtered_sum_basics, 2),
                'sumObligations' => number_format($filtered_sum_obligations, 2),
                'sumCopyright' => number_format($filtered_sum_copyright, 2),
                'net' => number_format($filtered_net, 2),
                'basicAccounts' => $filtered_basic_accounts,
                'obligationAccounts' => $filtered_obligation_accounts,
                'copyrightAccounts' => $filtered_copyright_accounts,
            ]);
        }

        return view('company.reports.balances',compact('net_supplier','net_tax','net_client','basics','sum_basics','obligations','sum_obligations','copyrights','sum_copyright','net'));


    }



}
