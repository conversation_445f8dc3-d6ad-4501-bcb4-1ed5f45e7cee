@extends('company.layout.CompanyLayout')
@section('title')
    تفاصيل الاشتراك
@endsection
@section('content')
    @if(Auth::user('company')->parent != 0)
        <div class="alert alert-danger">
            فقط الشركات الرئيسية يمكنها الوصول إلى صفحة الاشتراكات.
        </div>
        <script>
            window.location.href = "{{ route('company.home') }}";
        </script>
    @endif

    @cannot('subscription.show')
        <div class="alert alert-danger">
            ليس لديك صلاحية للوصول إلى تفاصيل الاشتراك.
        </div>
        <script>
            window.location.href = "{{ route('company.home') }}";
        </script>
    @endcannot

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                <div class="col-12">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif
                </div>
            </div>

            <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                <div class="col-xl-12">
                    <div class="card card-flush h-md-100">
                        <div class="card-header">
                            <div class="card-title">
                                <h2>تفاصيل الاشتراك</h2>
                            </div>
                            <div class="card-toolbar">
                                <a href="{{ route('company.subscription.select_plan') }}" class="btn btn-primary">
                                    {{ $activeSubscription ? 'تغيير الباقة' : 'اختيار باقة' }}
                                </a>
                            </div>
                        </div>
                        <div class="card-body pt-1">
                            @if($activeSubscription)
                                <div class="row mb-7">
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="fw-bold text-gray-600 fs-7">الحالة</div>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            <span class="badge badge-light-{{ $activeSubscription->status === 'active' ? 'success' : 'danger' }}">
                                                {{ $activeSubscription->status === 'active' ? 'نشط' : ($activeSubscription->status === 'expired' ? 'منتهي' : 'ملغي') }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="fw-bold text-gray-600 fs-7">الباقة</div>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            {{ $activeSubscription->plan ? $activeSubscription->plan->name : 'فترة تجريبية' }}
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-7">
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="fw-bold text-gray-600 fs-7">تاريخ البدء</div>
                                        <div class="fw-bold fs-6 text-gray-800">{{ $activeSubscription->start_date->format('Y/m/d') }}</div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="fw-bold text-gray-600 fs-7">تاريخ الانتهاء</div>
                                        <div class="fw-bold fs-6 text-gray-800">{{ $activeSubscription->end_date->format('Y/m/d') }}</div>
                                    </div>
                                </div>
                                <div class="row mb-7">
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="fw-bold text-gray-600 fs-7">الأيام المتبقية</div>
                                        <div class="fw-bold fs-6 text-gray-800">{{ Carbon\Carbon::today()->diffInDays($activeSubscription->end_date, false) }}</div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-12">
                                        <div class="fw-bold text-gray-600 fs-7">فترة تجريبية</div>
                                        <div class="fw-bold fs-6 text-gray-800">{{ $activeSubscription->is_trial ? 'نعم' : 'لا' }}</div>
                                    </div>
                                </div>
                                @if($activeSubscription->plan)
                                    <div class="row mb-7">
                                        <div class="col-lg-6 col-md-6 col-12">
                                            <div class="fw-bold text-gray-600 fs-7">مدة الباقة</div>
                                            <div class="fw-bold fs-6 text-gray-800">{{ $activeSubscription->plan->duration_days }} يوم</div>
                                        </div>
                                        <div class="col-lg-6 col-md-6 col-12">
                                            <div class="fw-bold text-gray-600 fs-7">سعر الباقة</div>
                                            <div class="fw-bold fs-6 text-gray-800">{{ $activeSubscription->plan->price }} @include('components.riyal-symbol', ['size' => '1em'])</div>
                                        </div>
                                    </div>
                                @endif
                                <!-- Free Trial Information -->
                                @if($activeSubscription->is_trial)
                                    <!-- Light Mode -->
                                    <div class="notice d-flex bg-light-primary rounded border-primary border border-dashed p-6 mb-7 theme-light-show">
                                        <i class="ki-duotone ki-gift fs-2tx text-primary me-4">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                            <span class="path4"></span>
                                        </i>
                                        <div class="d-flex flex-stack flex-grow-1">
                                            <div class="fw-semibold">
                                                <h4 class="text-gray-900 fw-bold">أنت تستخدم الفترة التجريبية المجانية</h4>
                                                <div class="fs-6 text-gray-700">استمتع بجميع مميزات النظام مجاناً لمدة 14 يوم. متبقي {{ Carbon\Carbon::today()->diffInDays($activeSubscription->end_date, false) }} يوم في فترتك التجريبية.</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Dark Mode -->
                                    <div class="notice d-flex rounded border-primary border border-dashed p-6 mb-7 theme-dark-show" style="background-color: #1e1e2d;">
                                        <i class="ki-duotone ki-gift fs-2tx text-primary me-4">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                            <span class="path4"></span>
                                        </i>
                                        <div class="d-flex flex-stack flex-grow-1">
                                            <div class="fw-semibold">
                                                <h4 class="text-white fw-bold">أنت تستخدم الفترة التجريبية المجانية</h4>
                                                <div class="fs-6 text-white">استمتع بجميع مميزات النظام مجاناً لمدة 14 يوم. متبقي {{ Carbon\Carbon::today()->diffInDays($activeSubscription->end_date, false) }} يوم في فترتك التجريبية.</div>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                @if($company->hasSubscriptionExpiringSoon())
                                    <!-- Light Mode -->
                                    <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed p-6 mb-7 theme-light-show">
                                        <i class="ki-duotone ki-information fs-2tx text-warning me-4">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                        <div class="d-flex flex-stack flex-grow-1">
                                            <div class="fw-semibold">
                                                <h4 class="text-gray-900 fw-bold">اشتراكك على وشك الانتهاء!</h4>
                                                <div class="fs-6 text-gray-700">سينتهي اشتراكك بتاريخ {{ $activeSubscription->end_date->format('Y/m/d') }} (خلال {{ Carbon\Carbon::today()->diffInDays($activeSubscription->end_date, false) }} يوم). يرجى تجديد اشتراكك للاستمرار في استخدام خدماتنا.</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Dark Mode -->
                                    <div class="notice d-flex rounded border-warning border border-dashed p-6 mb-7 theme-dark-show" style="background-color: #1e1e2d;">
                                        <i class="ki-duotone ki-information fs-2tx text-warning me-4">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                            <span class="path3"></span>
                                        </i>
                                        <div class="d-flex flex-stack flex-grow-1">
                                            <div class="fw-semibold">
                                                <h4 class="text-warning fw-bold">اشتراكك على وشك الانتهاء!</h4>
                                                <div class="fs-6 text-white">سينتهي اشتراكك بتاريخ {{ $activeSubscription->end_date->format('Y/m/d') }} (خلال {{ Carbon\Carbon::today()->diffInDays($activeSubscription->end_date, false) }} يوم). يرجى تجديد اشتراكك للاستمرار في استخدام خدماتنا.</div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            @else
                                <!-- Light Mode -->
                                <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed p-6 theme-light-show">
                                    <i class="ki-duotone ki-information fs-2tx text-warning me-4">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                    <div class="d-flex flex-stack flex-grow-1">
                                        <div class="fw-semibold">
                                            <h4 class="text-gray-900 fw-bold">لا يوجد اشتراك نشط</h4>
                                            <div class="fs-6 text-gray-700">ليس لديك اشتراك نشط. يرجى اختيار خطة اشتراك للاستمرار في استخدام خدماتنا.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Dark Mode -->
                                <div class="notice d-flex rounded border-warning border border-dashed p-6 theme-dark-show" style="background-color: #1e1e2d;">
                                    <i class="ki-duotone ki-information fs-2tx text-warning me-4">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                    <div class="d-flex flex-stack flex-grow-1">
                                        <div class="fw-semibold">
                                            <h4 class="text-warning fw-bold">لا يوجد اشتراك نشط</h4>
                                            <div class="fs-6 text-white">ليس لديك اشتراك نشط. يرجى اختيار خطة اشتراك للاستمرار في استخدام خدماتنا.</div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="card-footer pt-0">
                            <div class="d-flex flex-stack flex-wrap">
                                <div>
                                    <a href="{{ route('company.subscription.history') }}" class="btn btn-light btn-active-light-primary my-1 me-2">سجل الاشتراكات</a>
                                    <a href="{{ route('company.subscription.payment_history') }}" class="btn btn-light btn-active-light-primary my-1 me-2">سجل المدفوعات</a>
                                </div>
                                <a href="{{ route('company.subscription.index') }}" class="btn btn-light btn-active-light-primary my-1">العودة إلى نظرة عامة</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
