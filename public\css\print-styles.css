/* ========================================
   PROFESSIONAL PRINT STYLES - BEST PRACTICES
   Reusable across all reports
   ======================================== */

@media print {
    /* ========================================
       PAGE SETUP & BROWSER CONTROLS
       ======================================== */
    @page {
        size: A4 portrait;
        margin: 0;
    }
    
    html, body {
        height: 100%;
        margin: 0;
        padding: 0;
    }
    
    /* ========================================
       CONTENT VISIBILITY CONTROL
       ======================================== */
    body * {
        visibility: hidden;
    }
    
    #printable-section, 
    #printable-section * {
        visibility: visible;
    }
    
    #printable-section {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }
    
    /* ========================================
       TYPOGRAPHY & DIRECTION
       ======================================== */
    body {
        font-family: 'Beiruti', 'DejaVu Sans', sans-serif !important;
        direction: rtl !important;
        line-height: 1.6 !important;
    }
    
    /* ========================================
       LAYOUT COMPONENTS
       ======================================== */
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        margin: 0 !important;
        padding: 20px !important;
        background: #fff !important;
    }
    
    .card-header {
        background: #f8f9fa !important;
        border-bottom: 2px solid #dee2e6 !important;
        padding: 15px 20px !important;
        margin-bottom: 20px !important;
    }
    
    .card-title h2 {
        color: #000 !important;
        font-weight: bold !important;
        font-size: 24px !important;
        text-align: center !important;
        margin: 0 !important;
    }
    
    /* ========================================
       FORM ELEMENTS STYLING
       ======================================== */
    .form-control {
        border: 1px solid #ccc !important;
        background: #f9f9f9 !important;
        font-weight: bold !important;
        font-size: 16px !important;
        padding: 12px 15px !important;
        border-radius: 5px !important;
        color: #000 !important;
        display: block !important;
        visibility: visible !important;
    }
    
    .form-label {
        font-weight: 600 !important;
        color: #333 !important;
        font-size: 16px !important;
        display: block !important;
        visibility: visible !important;
    }
    
    /* ========================================
       ENSURE DATA VISIBILITY
       ======================================== */
    .fv-row,
    .fv-row *,
    .card-body,
    .card-body * {
        visibility: visible !important;
        display: block !important;
    }
    
    .fv-row {
        display: flex !important;
    }
    
    /* ========================================
       HIDE NON-PRINTABLE ELEMENTS
       ======================================== */
    .btn,
    .date, 
    .row.mt-12 {
        display: none !important;
    }
    
    /* ========================================
       SHOW PRINTABLE CONTENT
       ======================================== */
    .tab-content,
    .tab-pane {
        display: block !important;
        visibility: visible !important;
    }
    
    /* ========================================
       DATA ROWS STYLING
       ======================================== */
    .fv-row {
        margin-bottom: 20px !important;
        border-bottom: 1px solid #eee !important;
        padding-bottom: 15px !important;
        display: flex !important;
        align-items: center !important;
    }
    
    .fv-row:last-child {
        border-bottom: 2px solid #dee2e6 !important;
        background: #f8f9fa !important;
        padding: 15px !important;
        border-radius: 5px !important;
        font-weight: bold !important;
        margin-top: 20px !important;
    }
    
    /* ========================================
       COLUMN STYLING
       ======================================== */
    .col-md-2 {
        font-weight: 600 !important;
        color: #333 !important;
        flex: 0 0 30% !important;
    }
    
    .col-md-9 {
        text-align: left !important;
        flex: 0 0 70% !important;
    }
    
    /* ========================================
       REPORT HEADER & FOOTER
       ======================================== */
    #printable-section::before {
        content: attr(data-report-title, "تقرير");
        display: block;
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        color: #000;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #dee2e6;
    }
    
    #printable-section::after {
        content: "تاريخ التقرير: " attr(data-report-date);
        display: block;
        text-align: center;
        font-size: 12px;
        color: #666;
        margin-top: 20px;
        padding-top: 10px;
        border-top: 1px solid #eee;
    }
    
    /* ========================================
       PRINT-SPECIFIC ENHANCEMENTS
       ======================================== */
    .card-body {
        padding: 0 !important;
    }
    
    .tab-content {
        margin: 0 !important;
    }
    
    .d-flex {
        display: flex !important;
    }
    
    .flex-column {
        flex-direction: column !important;
    }
    
    .gap-7 {
        gap: 0 !important;
    }
    
    .gap-lg-10 {
        gap: 0 !important;
    }
    
    /* ========================================
       TABLE STYLING FOR REPORTS
       ======================================== */
    table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 10px 0 !important;
    }
    
    th, td {
        border: 1px solid #ddd !important;
        padding: 8px 12px !important;
        text-align: right !important;
        font-size: 14px !important;
    }
    
    th {
        background: #f8f9fa !important;
        font-weight: bold !important;
        color: #333 !important;
    }
    
    /* ========================================
       ADDITIONAL UTILITY CLASSES
       ======================================== */
    .text-center {
        text-align: center !important;
    }
    
    .text-right {
        text-align: right !important;
    }
    
    .text-left {
        text-align: left !important;
    }
    
    .font-bold {
        font-weight: bold !important;
    }
    
    .bg-light {
        background: #f8f9fa !important;
    }
    
    .border-bottom {
        border-bottom: 1px solid #dee2e6 !important;
    }
    
    .border-top {
        border-top: 1px solid #dee2e6 !important;
    }
} 