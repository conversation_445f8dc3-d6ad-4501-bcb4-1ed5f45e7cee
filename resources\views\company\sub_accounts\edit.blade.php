@extends('company.layout.CompanyLayout')
@section('title')
     تعديل حساب فرعي
@endsection
@section('content')
    <div id="kt_app_content_container" class="app-container container-xxl">
        <div class="card card-flush">
            <!--begin::Card header-->
            <div class="card-header pt-8">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2> تعديل حساب فرعي</h2>

                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body">
                    <form  class="form d-flex flex-column flex-lg-row" enctype="multipart/form-data"  action="{{  route('company.sub_accounts.update',$sub_account->id) }}"  method="post" id="form">
                        @csrf
                        {{ method_field('PATCH')}}
                        <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                            <div class="tab-content">
                                <div class="tab-pane fade show active"  role="tab-panel">
                                    <div class="d-flex flex-column gap-7 gap-lg-10">
                                        <div class="card card-flush py-4">


                                            <div class="card-body pt-0">

                                                <div class="fv-row row mb-15">
                                                    <div class="col-md-2 d-flex align-items-center">
                                                        <label class="required fs-2 fw-semibold">الحساب الرئيسي</label>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <select  class="form-control mb-2" placeholder="اختر الحساب الرئيسي"  id="parent_id">
                                                            <option>اختر</option>
                                                            @foreach($main_accounts as $account)
                                                                <option value="{{$account->id}}" @if($account->id==$main_account->parent_id)selected @endif>{{$account->name}}</option>
                                                            @endforeach

                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="fv-row row mb-15">
                                                    <div class="col-md-2 d-flex align-items-center">
                                                        <label class="required fs-2 fw-semibold">الحساب الفرعي</label>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <select  class="form-control mb-2" placeholder="اختر الحساب الرئيسي" name="parent_id" id="sub_account">
                                                            <option>اختر</option>
                                                            @foreach($sub_accounts as $account)
                                                                <option value="{{$account->id}}" @if($account->id==$sub_account->parent_id)selected @endif>{{$account->name}}</option>
                                                            @endforeach

                                                        </select>
                                                    </div>
                                                </div>



                                                <div class="fv-row row mb-15">
                                                    <div class="col-md-2 d-flex align-items-center">
                                                        <label class="required fs-2 fw-semibold">اسم الحساب الفرعي</label>
                                                    </div>
                                                    <div class="col-md-9">
                                                    <input type="text" name="name" class="form-control mb-2" placeholder="Product name" value="{{$sub_account->name}}"  name="parent_id" id="sub_account"/>
                                                    </div>
                                                </div>

                                                <div class="fv-row row mb-15">
                                                    <div class="col-md-2 d-flex align-items-center">
                                                        <label class="required fs-2 fw-semibold">الرصيد الافتتاحي </label>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <input type="text" name="balance" class="form-control mb-2"  value="{{$sub_account->balance}}" />
                                                    </div>
                                                </div>


                                                <!--begin::Action buttons-->
                                                <div class="row mt-12">
                                                    <div class="col-md-9 offset-md-3">
                                                        <!--begin::Cancel-->
                                                        <a href="{{route('company.sub_accounts.index')}}" class="btn btn-light me-5">رجوع</a>
                                                        <!--end::Cancel-->
                                                        <!--begin::Button-->
                                                        <button  type="submit" class="btn btn-primary" >
                                                            <span class="indicator-label">حفظ</span>
                                                            <span class="indicator-progress">يرجى الانتظار...
															<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                                        </button>
                                                        <!--end::Button-->
                                                    </div>
                                                </div>
                                                <!--begin::Action buttons-->
                                            </div>

                                            </div>
                                        </div>


                                    </div>
                                </div>

                        </div>
                    </form>
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>

@endsection

@section('script')
    <script src="{{asset('/manager_assest/dist/assets/js/custom/apps/ecommerce/catalog/save-product.js')}}"></script>
    <script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
    {!! JsValidator::formRequest('\App\Http\Requests\SubAccountsRequest', '#form') !!}


    <script>
        $(document).on('change','#parent_id', function(e){

            var parent_id = $(this).val();
            var url = '{{url("/company/get_sub_accounts")}}/'+parent_id;
            var csrf_token = '{{csrf_token()}}';
            $.ajax({
                type: 'GET',
                headers: {'X-CSRF-TOKEN': csrf_token},
                url: url,
                success: function (response) {
                    if (response.status == 'success') {
                        $('#sub_account').html("");
                        for(var i = 0 ;  response.sub.length >i; i++){
                            $('#sub_account').append('<option value="'+response.sub[i]['id']+'" >'+response.sub[i]['name']+'</option>');
                        }
                    }
                    else {
                    }
                },
                error: function (e) {
                }
            });
        });
    </script>

@endsection
