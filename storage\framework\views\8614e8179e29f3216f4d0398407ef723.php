<?php $__env->startSection('title'); ?>
    تقرير العملاء
<?php $__env->stopSection(); ?>
<?php $__env->startSection('css'); ?>
    <style>
        /* ========================================
           EXPERT CLIENTS REPORT PRINT STYLES
           Optimized for clients data presentation
           ======================================== */
        
        @media print {
            /* ========================================
               PAGE SETUP & BROWSER CONTROLS
               ======================================== */
            @page {
                size: A4 portrait;
                margin: 10mm;
                /* Remove default browser headers and footers */
                @top-center { content: ""; }
                @top-left { content: ""; }
                @top-right { content: ""; }
                @bottom-center { content: ""; }
                @bottom-left { content: ""; }
                @bottom-right { content: ""; }
            }
            
            html, body {
                height: auto;
                margin: 0;
                padding: 0;
                overflow: visible;
            }
            
            /* ========================================
               CONTENT VISIBILITY CONTROL
               ======================================== */
            body * {
                visibility: hidden;
            }
            
            #printable-section, 
            #printable-section * {
                visibility: visible;
            }
            
            #printable-section {
                position: relative;
                width: 100%;
                height: auto;
                overflow: visible;
            }
            
            /* ========================================
               TYPOGRAPHY & DIRECTION
               ======================================== */
            body {
                font-family: 'Beiruti', 'DejaVu Sans', sans-serif !important;
                direction: rtl !important;
                line-height: 1.3 !important;
            }
            
            /* ========================================
               LAYOUT COMPONENTS
               ======================================== */
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                margin: 0 !important;
                padding: 15px !important;
                background: #fff !important;
                width: 100% !important;
                height: auto !important;
            }
            
            .card-header {
                background: #f8f9fa !important;
                border-bottom: 2px solid #dee2e6 !important;
                padding: 10px 15px !important;
                margin-bottom: 15px !important;
            }
            
            /* ========================================
               HIDE NON-PRINTABLE ELEMENTS
               ======================================== */
            .btn,
            .fv-row,
            .form-control,
            .form-label {
                display: none !important;
            }
            
            /* ========================================
               TABLE STYLING - UNIFIED BLUE THEME
               ======================================== */
            table {
                width: 100% !important;
                border-collapse: collapse !important;
                margin: 10px 0 !important;
                font-size: 9px !important;
                text-align: center !important;
            }
            
            th, td {
                border: 1px solid #ddd !important;
                padding: 4px 6px !important;
                text-align: center !important;
                vertical-align: middle !important;
            }
            
            th {
                background: #e3f2fd !important;
                font-weight: bold !important;
                color: #1976d2 !important;
                font-size: 8px !important;
                border-bottom: 2px solid #2196f3 !important;
            }
            
            td {
                font-size: 8px !important;
                color: #000 !important;
            }
            
            /* ========================================
               SEQUENCE NUMBER COLUMN STYLING
               ======================================== */
            th:first-child,
            td:first-child {
                width: 50px !important;
                text-align: center !important;
                font-weight: bold !important;
            }
            
            /* ========================================
               UNIFIED DATA STYLING
               ======================================== */
            .table {
                border: 1px solid #dee2e6 !important;
            }
            
            .table thead th {
                background: #e3f2fd !important;
                border-bottom: 2px solid #2196f3 !important;
                color: #1976d2 !important;
            }
            
            .table tbody tr:nth-child(even) {
                background: #f8f9fa !important;
            }
            
            .table tbody tr:hover {
                background: #e3f2fd !important;
            }
            
            /* ========================================
               AMOUNT COLUMN STYLING
               ======================================== */
            td:nth-child(4), /* مدين column */
            td:nth-child(5) { /* دائن column */
                font-weight: bold !important;
                color: #2e7d32 !important;
                text-align: center !important;
            }
            
            /* ========================================
               REPORT HEADER & FOOTER
               ======================================== */
            #printable-section::before {
                content: "تقرير العملاء";
                display: block;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 15px;
                padding-bottom: 8px;
                border-bottom: 2px solid #2196f3;
            }
            
            #printable-section::after {
                content: "تاريخ التقرير: " attr(data-report-date);
                display: block;
                text-align: center;
                font-size: 10px;
                color: #666;
                margin-top: 15px;
                padding-top: 8px;
                border-top: 1px solid #eee;
            }
            
            /* ========================================
               PRINT-SPECIFIC ENHANCEMENTS
               ======================================== */
            .card-body {
                padding: 0 !important;
            }
            
            .align-middle {
                vertical-align: middle !important;
            }
            
            .text-start {
                text-align: center !important;
            }
            
            .text-gray-400 {
                color: #666 !important;
            }
            
            .fw-bold {
                font-weight: bold !important;
            }
            
            .fs-7 {
                font-size: 10px !important;
            }
            
            .text-uppercase {
                text-transform: none !important;
            }
            
            .gs-0 {
                gap: 0 !important;
            }
            
            .pe-2 {
                padding-right: 0 !important;
            }
            
            .min-w-100px {
                min-width: auto !important;
            }
            
            .text-gray-800 {
                color: #000 !important;
            }
            
            .fs-5 {
                font-size: 10px !important;
            }
            
            /* ========================================
               ENSURE COLOR PRINTING
               ======================================== */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            /* Note: DataTables responsive print fixes are now handled globally
               via /css/datatables-print-fix.css included in CompanyLayout.blade.php */

            /* ========================================
               REMOVE PAGINATION & PAGE BREAKS
               ======================================== */
            .pagination,
            .dataTables_paginate,
            .page-item,
            .page-link,
            .paginate_button,
            .dataTables_info,
            .dataTables_length,
            .dataTables_filter {
                display: none !important;
            }
            
            /* Prevent page breaks */
            table {
                page-break-inside: avoid !important;
            }
            
            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            /* Force single page */
            #printable-section {
                page-break-after: avoid !important;
                page-break-before: avoid !important;
            }
            
            /* ========================================
               FIX EMPTY SECOND PAGE
               ======================================== */
            .app-content,
            .app-container,
            .container-xxl {
                width: 100% !important;
                height: auto !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            
            /* Ensure content fits on one page */
            .card-flush {
                height: auto !important;
                min-height: auto !important;
            }
            
            /* ========================================
               HANDLE CONTENT OVERFLOW
               ======================================== */
            /* Allow content to flow to next page if needed */
            #printable-section {
                overflow: visible !important;
                height: auto !important;
                min-height: auto !important;
            }
            
            /* Ensure tables can break across pages if needed */
            table {
                page-break-inside: auto !important;
            }
            
            /* Allow rows to break if table is too large */
            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            /* Reduce font sizes for better fit */
            th {
                font-size: 9px !important;
                padding: 4px 6px !important;
            }
            
            td {
                font-size: 9px !important;
                padding: 4px 6px !important;
            }
            
            /* Reduce margins for more content space */
            @page {
                margin: 5mm !important;
            }
            
            /* Ensure all content is visible */
            .card {
                overflow: visible !important;
                height: auto !important;
                min-height: auto !important;
            }
            
            /* Allow content to scale down if needed */
            body {
                font-size: 90% !important;
            }
            
            /* Ensure no content is hidden */
            * {
                overflow: visible !important;
            }
        }
    </style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Products-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">

                    </div>
                    <!--end::Card title-->

                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <div class="fv-row row mb-15">
                        <form  class="row" method="get" action="<?php echo e(route('company.clients_report_filter')); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">من تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="from_date" id="from_date" class="form-control mb-2" value=""/>
                            </div>

                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">الى تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="to_date" id="to_date" class="form-control mb-2" value=""/>
                            </div>

                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">العملاء</label>
                            </div>
                            <div class="col-md-3">
                                <select name="client" class="form-control mb-2" >
                                    <option value="">اختر</option>
                                    <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($client->id); ?>"><?php echo e($client->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>




                            <!--begin::Action buttons-->
                            <div class="col-md-3">
                                <div class="col-md-9 offset-md-3">
                                    <!--begin::Cancel-->
                                    <a href="<?php echo e(route('company.clients_report')); ?>" class="btn btn-light me-5">رجوع</a>
                                    <!--end::Cancel-->
                                    <!--begin::Button-->
                                    <button  type="submit" class="btn btn-primary" >
                                        <span class="indicator-label">فلترة </span>
                                        <span class="indicator-progress">يرجى الانتظار...
															<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <!--end::Button-->
                                </div>
                            </div>
                            <!--begin::Action buttons-->

                        </form>
                    </div>
                    <!--begin::Table-->
                    <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                        <!--begin::Col-->
                        <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                            <!--begin::Card widget 20-->
                            <!--end::Card widget 20-->
                            <!--begin::Card widget 7-->
                            <div class="card card-flush h-md-100 mb-5 mb-xl-10">
                                <!--begin::Header-->
                                <div class="card-header pt-5">
                                    <!--begin::Title-->
                                    <div class="card-title d-flex flex-column">
                                        <!--begin::Amount-->
                                        <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2"><?php echo e($balance); ?></span>
                                        <!--end::Amount-->
                                        <!--begin::Subtitle-->
                                        <span class="text-gray-400 pt-1 fw-semibold fs-6">الرصيد الافتتاحي</span>
                                        <!--end::Subtitle-->
                                    </div>
                                    <!--end::Title-->
                                </div>
                                <!--end::Header-->
                                <!--begin::Card body-->

                                <!--end::Card body-->
                            </div>
                            <!--end::Card widget 7-->
                        </div>
                        <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                            <!--begin::Card widget 20-->
                            <!--end::Card widget 20-->
                            <!--begin::Card widget 7-->
                            <div class="card card-flush h-md-100 mb-5 mb-xl-10">
                                <!--begin::Header-->
                                <div class="card-header pt-5">
                                    <!--begin::Title-->
                                    <div class="card-title d-flex flex-column">
                                        <!--begin::Amount-->
                                        <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2"><?php echo e($debtor); ?></span>
                                        <!--end::Amount-->
                                        <!--begin::Subtitle-->
                                        <span class="text-gray-400 pt-1 fw-semibold fs-6">مدين</span>
                                        <!--end::Subtitle-->
                                    </div>
                                    <!--end::Title-->
                                </div>
                                <!--end::Header-->
                                <!--begin::Card body-->

                                <!--end::Card body-->
                            </div>
                            <!--end::Card widget 7-->
                        </div>
                        <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                            <!--begin::Card widget 20-->
                            <!--end::Card widget 20-->
                            <!--begin::Card widget 7-->
                            <div class="card card-flush h-md-100 mb-5 mb-xl-10">
                                <!--begin::Header-->
                                <div class="card-header pt-5">
                                    <!--begin::Title-->
                                    <div class="card-title d-flex flex-column">
                                        <!--begin::Amount-->
                                        <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2"><?php echo e($creditor); ?></span>
                                        <!--end::Amount-->
                                        <!--begin::Subtitle-->
                                        <span class="text-gray-400 pt-1 fw-semibold fs-6">دائن</span>
                                        <!--end::Subtitle-->
                                    </div>
                                    <!--end::Title-->
                                </div>
                                <!--end::Header-->
                                <!--begin::Card body-->

                                <!--end::Card body-->
                            </div>
                            <!--end::Card widget 7-->
                        </div>
                        <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                            <!--begin::Card widget 20-->
                            <!--end::Card widget 20-->
                            <!--begin::Card widget 7-->
                            <div class="card card-flush h-md-100 mb-5 mb-xl-10">
                                <!--begin::Header-->
                                <div class="card-header pt-5">
                                    <!--begin::Title-->
                                    <div class="card-title d-flex flex-column">
                                        <!--begin::Amount-->
                                        <span class="fs-2hx fw-bold text-dark me-2 lh-1 ls-n2"><?php echo e($debtor-$creditor); ?></span>
                                        <!--end::Amount-->
                                        <!--begin::Subtitle-->
                                        <span class="text-gray-400 pt-1 fw-semibold fs-6">الرصيد المتبقي</span>
                                        <!--end::Subtitle-->
                                    </div>
                                    <!--end::Title-->
                                </div>
                                <!--end::Header-->
                                <!--begin::Card body-->

                                <!--end::Card body-->
                            </div>
                            <!--end::Card widget 7-->
                        </div>



                        <!--end::Col-->
                    </div>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('clients_report.print')): ?>
                    <button onclick="printSection()" class="btn btn-success">طباعة</button>
                    <?php endif; ?>

                    <div id="printable-section" data-report-title="تقرير العملاء" data-report-date="<?php echo e(now()->format('Y-m-d H:i')); ?>">
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="datatable">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">#</th>
                            <th class="min-w-100px">التاريخ</th>
                            <th class="min-w-100px">التفاصيل</th>
                            <th class="min-w-100px">مدين</th>
                            <th class="min-w-100px">دائن</th>
                            <th class="min-w-100px">المتبقي</th>
                            <th class="min-w-100px">الحساب</th>
                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">






                        </tbody>
                    </table>
                    <!--end::Table-->
                    </div>
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->
        </div>
        <!--end::Content container-->
    </div>




<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
    <script src="<?php echo e(asset('/manager_assest/dist/assets/plugins/custom/datatables/datatables.bundle.js')); ?>"></script>

    <script>
        var table = $('#datatable');
        var isPrinting = false;

        // begin first table
        table.DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ordering: false,
            searching: false, // Disable searching
            pageLength: 15,
            lengthMenu: [[10, 15, 25, 50, 100], [10, 15, 25, 50, 100]],

            ajax: {
                url: '<?php echo e(route('company.clients_report')); ?>',
                data: function (d) {
                    d.search = $("#search").val();
                    d.from_date = $("#from_date").val();
                    d.to_date = $("#to_date").val();
                    // If printing, request all data
                    if (isPrinting) {
                        d.length = -1; // Request all records
                        d.start = 0;
                    }
                }
            },
            columns: [
                {
                    data: null,
                    name: 'sequence',
                    orderable: false,
                    searchable: false,
                    render: function (data, type, row, meta) {
                        return meta.row + meta.settings._iDisplayStart + 1;
                    }
                },
                {data: 'date', name: 'date'},
                {data: 'details', name: 'details'},
                {data: 'debtor', name: 'debtor'},
                {data: 'creditor', name: 'creditor'},
                {data: 'change', name: 'change'},
                {data: 'account_name', name: 'account_name'},
            ],
            dom: 'Bfrtip', // Removed 'f' (filter/search) from DOM
            buttons: []
        });

        // Print function that loads all data and handles responsive tables
        function printSection() {
            isPrinting = true;

            // Show loading indicator
            const printBtn = document.querySelector('button[onclick="printSection()"]');
            const originalText = printBtn.innerHTML;
            printBtn.innerHTML = 'جاري تحميل البيانات...';
            printBtn.disabled = true;

            // Store original responsive state
            const dataTable = table.DataTable();
            const originalResponsive = dataTable.responsive;

            // Clear current table data
            dataTable.clear().draw();

            // Reload with all data
            dataTable.ajax.reload(function(json) {
                // Force all columns to be visible for printing
                if (originalResponsive && originalResponsive.hasHidden()) {
                    // Temporarily disable responsive and show all columns
                    originalResponsive.recalc();

                    // Force show all hidden columns
                    const hiddenColumns = dataTable.columns('.dtr-hidden');
                    hiddenColumns.nodes().to$().removeClass('dtr-hidden').show();

                    // Add print-ready class to table
                    $('#datatable').addClass('print-ready');
                }

                // After data is loaded, trigger print
                setTimeout(function() {
                    window.print();

                    // Reset button and printing flag
                    printBtn.innerHTML = originalText;
                    printBtn.disabled = false;
                    isPrinting = false;

                    // Remove print-ready class
                    $('#datatable').removeClass('print-ready');

                    // Reload normal pagination after printing
                    dataTable.ajax.reload(function() {
                        // Restore responsive behavior
                        if (originalResponsive) {
                            originalResponsive.recalc();
                        }
                    });
                }, 500);
            });
        }

        $('#search').keyup(function(){
            table.DataTable().draw(true);
        });

        $('#from_date').on('change', function() {
            table.DataTable().draw(true);
        });
        
        $('#to_date').on('change', function() {
            table.DataTable().draw(true);
        });

        $('select[name="client"]').on('change', function() {
            table.DataTable().draw(true);
        });
    </script>

<?php $__env->stopSection(); ?>



<?php echo $__env->make('company.layout.CompanyLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\dasta\resources\views/company/reports/clients_report.blade.php ENDPATH**/ ?>