<?php


use App\Http\Controllers\Company\EmployeeController;
use App\Http\Controllers\Company\NotificationController;
use Illuminate\Support\Facades\Route;

Route::get('/home', function () {
    $users[] = Auth::user();
    $users[] = Auth::guard()->user();
    $users[] = Auth::guard('company')->user();

    //dd($users);

    return view('company.home');
})->name('home');


Route::group(['namespace' => 'Company'], function () {




   Route::get('/re_inat', 'SettingController@inat')->name('inat');


    Route::get('/home', 'HomeController@home')->name('home');
    Route::get('/notifications', [NotificationController::class, 'getNotifications'])->name('get-notifications');
    Route::post('/notifications/{notification}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');

    Route::resource('sub_accounts','SubAccountsController');
    Route::resource('categories','CategoryController');
    Route::resource('products','ProductController');
    Route::resource('suppliers','SupplierController');
    Route::resource('clients','ClientController');
    Route::resource('employees','EmployeeController');
    Route::resource('roles','RoleController');
    Route::resource('return_sales','ReSaleController');
    Route::resource('return_purchases','RePurchaseController');

    Route::get('/employees/{employee}/permissions', [EmployeeController::class, 'permissions'])
        ->name('employees.permissions');
    Route::post('/employees/{employee}/permissions', [EmployeeController::class, 'permissionsChange'])
        ->name('employees.permissions');



    Route::get('/get_sub_accounts/{id}', 'SubAccountsController@get_sub_accounts');
    Route::get('/get_invoice_products/{id}', 'ReSaleController@get_invoice_products');


    Route::get('/get_invoices_for_repurchases/{type}', 'RePurchaseController@get_invoices_for_repurchases');
    Route::get('/get_invoices_for_resale/{type}', 'ReSaleController@get_invoices_for_resale');





    Route::get('/sales_invoice_index', 'SaleInvoiceController@index')->name('sales.invoice');
    Route::get('/create_sales_invoice', 'SaleInvoiceController@create')->name('create.sales.invoice');
    Route::post('/store_sales_invoice', 'SaleInvoiceController@store')->name('store.sales.invoice');
    Route::get('/add_product_to_invoice', 'SaleInvoiceController@add_product_to_invoice')->name('add_product_to_invoice');
    Route::delete('/delete_product_from_invoice/{id}','SaleInvoiceController@delete_product_from_invoice')->name('delete_product_from_invoice');
    Route::get('/edit_sales_invoice/{id}', 'SaleInvoiceController@edit')->name('edit.sales.invoice');
    Route::post('/store_done_sales_invoice/{id}', 'SaleInvoiceController@store_done')->name('store_done.sales.invoice');
    Route::delete('/delete_sales_invoice/{id}', 'SaleInvoiceController@destroy')->name('destroy.sales.invoice');
    Route::get('/s_print/{id}', 'SaleInvoiceController@print')->name('print.sales');
    Route::get('/p_print/{id}', 'PurchaseInvoiceController@print')->name('print.purchase');

    Route::get('/get_price_product/{id}', 'SaleInvoiceController@get_price_product')->name('get_price_product');


    Route::get('/create_payments/{id}', 'SaleInvoiceController@create_payments')->name('invoice.create.payments');
    Route::post('/store_payments', 'SaleInvoiceController@store_payments')->name('invoice.store.payments');
    Route::get('/search_product', 'SaleInvoiceController@search_products')->name('search.product');


    Route::get('/receipts', 'BillsController@index_receipts')->name('bills.receipts');
    Route::get('/create_receipt', 'BillsController@create_receipt')->name('bills.create_receipt');
    Route::post('/receipts', 'BillsController@store_receipt')->name('bills.store_receipts');

    Route::get('/receipt_details/{id}', 'BillsController@receipt_details')->name('bills.receipts.details');
    Route::get('/invoice_receipts/{id}', 'BillsController@index_invoice_receipts')->name('bills.invoice.receipts');
    Route::get('/account_operations/{id}', 'SubAccountsController@operations_index')->name('account.operations');
    Route::get('/account_operations_client/{id}', 'ClientController@operations_index')->name('client.operations');
    Route::get('/account_operations_supplier/{id}', 'SupplierController@operations_index')->name('supplier.operations');


    Route::get('/purchases_invoice_index', 'PurchaseInvoiceController@index')->name('purchases.invoice');
    Route::get('/create_purchases_invoice', 'PurchaseInvoiceController@create')->name('create.purchases.invoice');
    Route::post('/store_purchases_invoice', 'PurchaseInvoiceController@store')->name('store.purchases.invoice');
    Route::get('/add_product_to_purchases_invoice', 'PurchaseInvoiceController@add_product_to_invoice')->name('add_product_to_purchases_invoice');
    Route::delete('/delete_product_purchases_invoice/{id}','PurchaseInvoiceController@delete_product_from_invoice')->name('delete_product_purchases_invoice');
    Route::get('/search_product_purchases', 'PurchaseInvoiceController@search_products')->name('search.purchases.product');


    Route::get('/offers_invoice_index', 'OfferInvoiceController@index')->name('offers.invoice');
    Route::get('/create_offers_invoice', 'OfferInvoiceController@create')->name('create.offers.invoice');
    Route::post('/store_offers_invoice', 'OfferInvoiceController@store')->name('store.offers.invoice');
    Route::get('/add_product_to_offers_invoice', 'OfferInvoiceController@add_product_to_invoice')->name('add_product_to_offers_invoice');
    Route::get('/detail_offers_invoice/{id}', 'OfferInvoiceController@detail_offers_invoice')->name('detail_offers_invoice');
    Route::delete('/delete_product_offers_invoice/{id}','OfferInvoiceController@delete_product_from_invoice')->name('delete_product_offers_invoice');




    Route::get('/expenses', 'BillsController@index_expenses')->name('bills.expenses');
    Route::get('/create_expenses', 'BillsController@create_expenses')->name('bills.create_expenses');
    Route::post('/expenses', 'BillsController@store_expenses')->name('bills.store_expenses');
    Route::get('/expense_details/{id}', 'BillsController@expense_details')->name('bills.expenses.details');
    Route::get('/invoice_expenses/{id}', 'BillsController@index_invoice_expenses')->name('bills.invoice.expenses');

    Route::get('/create_purchases_payments/{id}', 'PurchaseInvoiceController@create_payments')->name('invoice_purchases.create.payments');
    Route::post('/store_purchases_payments', 'PurchaseInvoiceController@store_payments')->name('invoice_purchases.store.payments');


    Route::resource('/daly_expenses', 'ExpensesController');
    Route::resource('/job_titles', 'JobTitleController');
    Route::resource('/vacations', 'VacationController');
    Route::get('/print/{id}', 'VacationController@print')->name('print');
    Route::resource('/housing_allowances', 'HousingAllowanceController');
    Route::resource('/resignations', 'ResignationController');
    Route::resource('/handovers', 'HandoverController');
    Route::resource('/claims', 'ClaimController');
    Route::resource('/salaries', 'SalaryController');
    Route::resource('/travels', 'TravelController');
    Route::resource('/evaluations', 'EvaluationController');
    Route::resource('/tasks', 'TaskController');


    Route::get('/joining_after_vacation/{id}', 'VacationController@joining_after_vacation')->name('joining_after_vacation');
    Route::patch('/update_joining_after/{id}', 'VacationController@update_joining_after')->name('update_joining_after');

    Route::get('/income_list_report', 'ReportController@income_list_report')->name('income_list_report');
    Route::get('/account_statement', 'ReportController@account_statement')->name('account_statement');
    Route::get('/account_statement_filter', 'ReportController@account_statement_filter')->name('account_statement_filter');
    Route::get('/clients_report', 'ReportController@clients_report')->name('clients_report');
    Route::get('/suppliers_report', 'ReportController@suppliers_report')->name('suppliers_report');
    Route::get('/sales_report', 'ReportController@sales_report')->name('sales_report');
    Route::get('/purchases_report', 'ReportController@purchases_report')->name('purchases_report');
    Route::get('/expenses_report', 'ReportController@expenses_report')->name('expenses_report');
    Route::get('/products_report', 'ReportController@products_report')->name('products_report');
    Route::get('/tax_report', 'ReportController@tax_report')->name('tax_report');
    Route::get('/trial_balance', 'ReportController@trial_balance')->name('trial_balance');
    Route::get('/balances', 'ReportController@balances')->name('balances');


    Route::get('/clients_report_filter', 'ReportController@clients_report_filter')->name('clients_report_filter');
    Route::get('/suppliers_report_filter', 'ReportController@suppliers_report_filter')->name('suppliers_report_filter');
    Route::get('/sales_report_filter', 'ReportController@sales_report_filter')->name('sales_report_filter');
    Route::get('/purchases_report_filter', 'ReportController@purchases_report_filter')->name('purchases_report_filter');
    Route::get('/purchases_report_filter', 'ReportController@purchases_report_filter')->name('purchases_report_filter');
    Route::get('/expenses_report_filter', 'ReportController@expenses_report_filter')->name('expenses_report_filter');

    Route::resource('/transfer', 'TransferController');
    Route::get('/get_to_accounts/{id}', 'TransferController@subAccount');

    Route::get('settings', 'SettingController@settings')->name('settings.general');
    Route::post('settings', 'SettingController@updateSettings')->name('settings.updateSettings');
    Route::get('/initialization', 'SettingController@initialization');

    Route::get('/index_payroll/{id}', 'EmployeeController@index_payroll')->name('index.payroll');
    Route::get('/print_payroll/{id}', 'EmployeeController@print_payroll')->name('print.payroll');
    Route::get('/payroll/{id}', 'EmployeeController@payroll')->name('add.payroll');
    Route::post('/payrolls', 'EmployeeController@post_payroll')->name('payrolls');

    Route::resource('/list_vacations', 'ListVacationController');
    Route::get('/print_vacations/{id}', 'ListVacationController@print_vacations')->name('print.vacations');;
    Route::get('/filter/vacations', 'ListVacationController@filter')->name('vacations.filter');

    Route::resource('/managements', 'ManagementController');
    Route::resource('/departments', 'DepartmentController');
    Route::get('/get_departments_by_id_management/{id}', 'DepartmentController@get_departments_by_id_management');

    // Subscription Routes
    Route::get('/subscription', 'SubscriptionController@index')->name('subscription.index');
    Route::get('/subscription/details', 'SubscriptionController@show')->name('subscription.show');
    Route::get('/subscription/select-plan', 'SubscriptionController@selectPlan')->name('subscription.select_plan');
    Route::post('/subscription/process-plan', 'SubscriptionController@processPlan')->name('subscription.process_plan');
    Route::get('/subscription/history', 'SubscriptionController@history')->name('subscription.history');
    Route::get('/subscription/payments', 'SubscriptionController@paymentHistory')->name('subscription.payment_history');
    Route::get('/subscription/payments/{id}', 'SubscriptionController@paymentDetails')->name('subscription.payment_details');

    // Subscription Payment Routes
    Route::get('/subscription/payment/confirmation', 'SubscriptionController@paymentConfirmation')->name('subscription.payment_confirmation');
    Route::post('/subscription/payment/initiate', 'SubscriptionController@initiatePayment')->name('subscription.initiate_payment');

    // Callback route - exempt from CSRF protection
    Route::get('/subscription/payment/callback', 'SubscriptionController@paymentCallback')
        ->name('subscription.payment_callback')
        ->withoutMiddleware(['csrf']);

    Route::get('/subscription/payment/success', 'SubscriptionController@paymentSuccess')->name('subscription.payment_success');
    Route::get('/subscription/payment/pending/{paymentId}', 'SubscriptionController@paymentPending')->name('subscription.payment_pending');
    Route::get('/subscription/payment/check/{paymentId}', 'SubscriptionController@checkPaymentStatus')->name('subscription.check_payment');

    Route::get('/payroll', 'PayrollController@index')->name('payroll.index');
    Route::get('/filter/payroll', 'PayrollController@filter')->name('payroll.filter');

});
