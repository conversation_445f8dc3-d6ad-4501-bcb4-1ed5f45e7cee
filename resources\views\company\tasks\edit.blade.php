@extends('company.layout.CompanyLayout')
@section('title')
     تعديل المهمة
@endsection
@section('content')
    <div id="kt_app_content_container" class="app-container container-xxl">
        <div class="card card-flush">
            <!--begin::Card header-->
            <div class="card-header pt-8">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2>تعديل المهمة</h2>
                </div>
                <!--end::Card title-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body">
        <form  class="form d-flex flex-column flex-lg-row" enctype="multipart/form-data"  action="{{  route('company.tasks.update',$task->id) }}"  method="post" id="form">
            @csrf
            {{ method_field('PATCH')}}
            <div class="d-flex flex-column flex-row-fluid gap-7 gap-lg-10">
                <div class="tab-content">
                    <div class="tab-pane fade show active"  role="tab-panel">
                        <div class="d-flex flex-column gap-7 gap-lg-10">
                            <div class="card card-flush py-4">



                                <div class="card-body pt-0">

                                    <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="required form-label">الموظف</label>
                                        </div>
                                        <div class="col-md-9">
                                            <select name="employee_id" class="form-control mb-2" >
                                                <option value="">اختر</option>
                                                @foreach($employees as $employee)
                                                    <option value="{{$employee->id}}" @if($task->employee_id==$employee->id) selected @endif>{{$employee->name}}</option>
                                                @endforeach

                                            </select>
                                        </div>
                                    </div>

                                    <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="required form-label">العميل</label>
                                        </div>
                                        <div class="col-md-9">
                                            <select name="client_id" class="form-control mb-2" >
                                                <option value="">اختر</option>
                                                @foreach($clients as $client)
                                                    <option value="{{$client->id}}" @if($task->client_id==$client->id) selected @endif>{{$client->name}}</option>
                                                @endforeach

                                            </select>
                                        </div>
                                    </div>

                                    <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="required form-label">الاسم</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" name="name" class="form-control mb-2" value="{{$task->name}}"/>
                                        </div>
                                    </div>
                                    <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="required form-label">التفاصيل</label>
                                        </div>
                                        <div class="col-md-9">
                                            <textarea  name="details" class="form-control mb-2">{{$task->details}}</textarea>
                                        </div>
                                    </div>

                                    <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="required form-label">تاريخ البداية</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="date" name="start_date" class="form-control mb-2" value="{{$task->start_date}}"/>
                                        </div>
                                    </div>
                                    <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="required form-label">تاريخ العودة للعمل</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="date" name="end_date" class="form-control mb-2" value="{{$task->end_date}}"/>
                                        </div>
                                    </div>
                                    <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="required form-label">تاريخ النهاية</label>
                                        </div>
                                        <div class="col-md-9">
                                            <select name="status" class="form-control mb-2" >
                                                <option value="">اختر</option>
                                                <option value="open" @if($task->status=='open') selected @endif>مفتوحة</option>
                                                <option value="stop" @if($task->status=='stop') selected @endif>موقوفة</option>
                                                <option value="process" @if($task->status=='process') selected @endif>جارية</option>
                                                <option value="close" @if($task->status=='close') selected @endif>منتهية</option>

                                            </select>
                                        </div>
                                    </div>


                                    <a href="{{$task->file}}">الملف المرفق</a>

                                     <div class="fv-row row mb-15">
                                        <div class="col-md-2 d-flex align-items-center">
                                            <label class="required form-label">الملف</label>
                                        </div>
                                        <div class="col-md-9">
                                            <input type="file" name="file" class="form-control mb-2" />
                                        </div>
                                    </div>

                                </div>


                            </div>

                        </div>
                    </div>

                    <!--begin::Action buttons-->
                    <div class="row mt-12">
                        <div class="col-md-9 offset-md-3">
                            <!--begin::Cancel-->
                            <a href="{{route('company.tasks.index')}}" class="btn btn-light me-5">رجوع</a>
                            <!--end::Cancel-->
                            <!--begin::Button-->
                            <button  type="submit" class="btn btn-primary" >
                                <span class="indicator-label">حفظ</span>
                                <span class="indicator-progress">يرجى الانتظار...
															<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                            </button>
                            <!--end::Button-->
                        </div>
                    </div>
                    <!--begin::Action buttons-->
                </div>

            </div>
        </form>
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>

@endsection

@section('script')
    <script src="{{asset('/manager_assest/dist/assets/js/custom/apps/ecommerce/catalog/save-product.js')}}"></script>
    <script type="text/javascript" src="{{ asset('vendor/jsvalidation/js/jsvalidation.js')}}"></script>
    {!! JsValidator::formRequest('\App\Http\Requests\TaskRequest', '#form') !!}

@endsection
