<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\PurchasesInvoiceRequest;
use App\Http\Requests\PaymentRequest;
use App\Models\AccountOperation;
use App\Models\Category;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\InvoiceProductCart;
use App\Models\Product;
use App\Models\Receipt;
use App\Models\SubAccount;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Salla\ZATCA\GenerateQrCode;
use Salla\ZATCA\Tags\InvoiceDate;
use Salla\ZATCA\Tags\InvoiceTaxAmount;
use Salla\ZATCA\Tags\InvoiceTotalAmount;
use Salla\ZATCA\Tags\Seller;
use Salla\ZATCA\Tags\TaxNumber;
use Yajra\DataTables\DataTables;
use Auth;
use Illuminate\Support\Facades\Redirect;


class PurchaseInvoiceController extends Controller
{

    public function index()
    {
        $this->authorize('purchases_invoice.index');

        $company=Auth::guard('company')->user();
        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }

        if (request()->ajax())
        {
            $data = Invoice::query()->where('type_invoices','purchases')->where('company_id',$company_id)->latest();
            $search = request()->get('search', false);
            $status = request()->get('status', false);
            if(!empty($search) || $search != '') {
                $data = $data->where('name', 'like', '%' . $search. '%');
            }
            if(!empty($status) || $status != '') {
                $data = $data->where('status',$status);
            }
            return DataTables::make($data)
                ->escapeColumns([])
                ->addColumn('actions', function ($row) {
                    return view('company.purchase_invoices.buttons', compact('row'));
                })
                ->make();
        }

        return view('company.purchase_invoices.index');
    }
    public function create()
    {
        $this->authorize('purchases_invoice.create');

        $company_id=Auth::guard('company')->user()->id;
        $suppliers=Supplier::query()->where('status','active')->where('company_id',$company_id)->get();
        $categories=Category::query()->where('status','active')->where('company_id',$company_id)->get();
        $products=Product::query()->where('status','active')->where('company_id',$company_id)->orWhere('type','service')->where('company_id',$company_id)->latest()->get();
        $banks=SubAccount::query()->where('parent_id',18)->where('company_id',$company_id)->get();
        $cashs=SubAccount::query()->where('parent_id',7)->where('company_id',$company_id)->get();
        $invoice_products = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','purchases')->get();
        $total_price=0;


        foreach ($invoice_products as $result){
            $total_price+= ($result->count*$result->price);

        }

//        $company_tax=Auth::guard('company')->user()->tax;
//
//        if ($company_tax==1){
//
//            $tax_price=0.15*$total_price;
//
//        }else{
//
//            $tax_price=0;
//        }
        $net_price=$total_price;

        return view('company.purchase_invoices.create',compact('suppliers','products','invoice_products','total_price','net_price','categories','banks','cashs'));
    }
    public function store(PurchasesInvoiceRequest $request)
    {
        $this->authorize('purchases_invoice.create');

        $company_id=Auth::guard('company')->user()->id;

        $data = $request->validated();
        $total=0;

        $results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','purchases')->get();
        $count_results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','purchases')->count();
        $counts = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','purchases')->sum('count');

        if ($count_results==0){

            return Redirect::back()->withErrors(['msg' => 'يجب عليك ادخال منتجات في الفاتورة']);

        }

        foreach ($results as $result){
            $total+= ($result->count*$result->price);

        }


        if ($data['type_discount']=='percentage'){

            $discount_amount=($data['discount']/100)*$total;
        }elseif($data['type_discount']=='amount'){

            $discount_amount=$data['discount'];
        }else{

            $discount_amount=0;
        }

        $total_taxable=$total-$discount_amount;


        $company_tax=$data['type_tax'];

        if ($company_tax=='tax_15'){

            $tax_price=0.15*$total_taxable;
        }else{

            $tax_price=0;
        }
        $payment_amount=$total_taxable+$tax_price;

        if ($data['payment_status']=='paid'){

            $data['payment_amount']=$payment_amount;

            if ($data['payment_method']=='cash'){

                $data['sub_account_id']=$data['cash_sub_account_id'];
                $sub=SubAccount::query()->where('id',$data['cash_sub_account_id'])->first()->net_balance;

                if ($payment_amount>$sub){

                    return Redirect::back()->withErrors(['msg' => 'قيمة الفاتورة اكبر من رصيد الحساب']);

                }

            }else{

                $data['sub_account_id']=$data['bank_sub_account_id'];

                $sub=SubAccount::query()->where('id',$data['bank_sub_account_id'])->first()->net_balance;
                if ($payment_amount>$sub){

                    return Redirect::back()->withErrors(['msg' => 'قيمة الفاتورة اكبر من رصيد الحساب']);

                }


            }
        }

        if ($data['payment_method']=='cash'){

            $data['sub_account_id']=$data['cash_sub_account_id'];


            $data['sub_account_id']=$data['cash_sub_account_id'];
            $sub=SubAccount::query()->where('id',$data['cash_sub_account_id'])->first();
//            $sub->update(['balance'=>$sub->balance-$data['payment_amount']]);

        }elseif($data['payment_method']=='bank'){

            $data['sub_account_id']=$data['bank_sub_account_id'];
            $data['sub_account_id']=$data['bank_sub_account_id'];
            $sub=SubAccount::query()->where('id',$data['bank_sub_account_id'])->first();
//            $sub->update(['balance'=>$sub->balance-$data['payment_amount']]);

        }


        $data['tax_amount']=$tax_price;
        $data['discount_amount']=$discount_amount;
        $data['total']=$total;
        $data['total_taxable']=$total_taxable;
        $data['company_id']=$company_id;
        $data['created_by']=$company_id;
        $data['type_invoices']='purchases';







        $done= Invoice::query()->create($data);

        $company_tax_number=Auth::guard('company')->user()->tax_number;

        $qrCode = GenerateQrCode::fromArray([
            new Seller($done->supplier_id), // seller name
            new TaxNumber($company_tax_number), // seller tax number
            new InvoiceDate($done->created_at), // invoice date as Zulu ISO8601 @see https://en.wikipedia.org/wiki/ISO_8601
            new InvoiceTotalAmount($done->total_taxable), // invoice total amount
            new InvoiceTaxAmount($done->tax_amount) // invoice tax amount
            // .......
        ])->render();


        $count = Invoice::query()->where('type_invoices','purchases')->where('company_id',$company_id)->count();

        Invoice::query()->where('id',$done->id)->first()->update(['qr_code_image'=>$qrCode,'number_code'=>'BILL'.$count]);



        if ($done){

            foreach ($results as $result){

                if ($data['type_discount']=='percentage'){

                    $discount_amount_product=($data['discount']/100)*$result->price;
                }elseif($data['type_discount']=='amount'){

                    $discount_amount_product=$data['discount']/$counts;
                }else{

                    $discount_amount_product=0;
                }

                $company_tax=$data['type_tax'];

                if ($company_tax=='tax_15'){

                    $tax_price_product=0.15*$result->price;

                }else{

                    $tax_price_product=0;

                }


                $product_data['product_id']=$result->product_id;
                $product_data['invoice_id']=$done->id;
                $product_data['code_number']=$result->product_id;
                $product_data['count']=$result->count;
                $product_data['net_count']=$result->count;
                $product_data['net_price']=$result->price;
                $product_data['price']=($result->price-$discount_amount_product)+$tax_price_product;
                $product_data['total']=$result->count*($result->price-$discount_amount_product);


                InvoiceProduct::query()->create($product_data);

                $product= Product::query()->where('id',$result->product_id)->first();
                if ($product->type=='product'){
                    $product->update(['opening_balance'=>$product->opening_balance+$result->count]);
                }

            }


        }


        InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','purchases')->delete();

        if ($done->getRawOriginal('payment_status')=='paid'){


            $receipt_data_supplier['amount']=$done->payment_amount;
            $receipt_data_supplier['date']=$done->date;
            $receipt_data_supplier['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_supplier['sub_account']=0;
            $receipt_data_supplier['main_account']=0;
            $receipt_data_supplier['type_bonds']='supplier';
            $receipt_data_supplier['invoice_id']=$data['supplier_id'];
            $receipt_data['invoice_id']=$done->id;
            $receipt_data_supplier['company_id']=$company_id;


            ////

            $operation_data_supplier['creditor']=$done->payment_amount;
            $operation_data_supplier['debtor']=0;
            $operation_data_supplier['date']=$done->date;
            $operation_data_supplier['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_supplier['sub_account']=0;
            $operation_data_supplier['type']='supplier';
            $operation_data_supplier['source_id']=$data['supplier_id'];
            $operation_data_supplier['invoice_id']=$done->id;
            $operation_data_supplier['company_id']=$company_id;





            Receipt::query()->create($receipt_data_supplier);
            AccountOperation::query()->create($operation_data_supplier);



            $sub_account=SubAccount::query()->where('type_account','purchase')->where('company_id',$company_id)->first();

            $receipt_data_purchase['amount']=$total_taxable;
            $receipt_data_purchase['date']=$done->date;
            $receipt_data_purchase['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_purchase['sub_account']=$sub_account->id;
            $receipt_data_purchase['main_account']=$sub_account->parent_id;
            $receipt_data_purchase['type_bonds']='supplier';
            $receipt_data_purchase['invoice_id']=$data['supplier_id'];
            $receipt_data_purchase['invoice_id']=$done->id;
            $receipt_data_purchase['company_id']=$company_id;


            ////

            $operation_data_purchase['creditor']=0;
            $operation_data_purchase['debtor']=$total_taxable;
            $operation_data_purchase['date']=$done->date;
            $operation_data_purchase['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_purchase['sub_account']=$sub_account->id;
            $operation_data_purchase['source_id']=$done->id;
            $operation_data_purchase['invoice_id']=$done->id;
            $operation_data_purchase['company_id']=$company_id;






            Receipt::query()->create($receipt_data_purchase);
            AccountOperation::query()->create($operation_data_purchase);



            $sub_account=SubAccount::query()->where('type_account','tax_purchase')->where('company_id',$company_id)->first();

            $receipt_data_tax['amount']=$tax_price;
            $receipt_data_tax['date']=$done->date;
            $receipt_data_tax['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_tax['sub_account']=$sub_account->id;
            $receipt_data_tax['main_account']=$sub_account->parent_id;
            $receipt_data_tax['type_bonds']='supplier';
            $receipt_data_tax['invoice_id']=$data['supplier_id'];
            $receipt_data_tax['invoice_id']=$done->id;
            $receipt_data_tax['company_id']=$company_id;


            ////

            $operation_data_tax['creditor']=0;
            $operation_data_tax['debtor']=$tax_price;
            $operation_data_tax['date']=$done->date;
            $operation_data_tax['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_tax['sub_account']=$sub_account->id;
            $operation_data_tax['source_id']=$done->id;
            $operation_data_tax['invoice_id']=$done->id;
            $operation_data_tax['company_id']=$company_id;






            Receipt::query()->create($receipt_data_tax);
            AccountOperation::query()->create($operation_data_tax);


            ///////////////////


            $receipt_data_supplier1['amount']=$done->payment_amount;
            $receipt_data_supplier1['date']=$done->date;
            $receipt_data_supplier1['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_supplier1['sub_account']=0;
            $receipt_data_supplier1['main_account']=0;
            $receipt_data_supplier1['type_bonds']='supplier';
            $receipt_data_supplier1['invoice_id']=$data['supplier_id'];
            $receipt_data_supplier1['invoice_id']=$done->id;
            $receipt_data_supplier1['company_id']=$company_id;



            ////

            $operation_data_supplier1['creditor']=0;
            $operation_data_supplier1['debtor']=$done->payment_amount;
            $operation_data_supplier1['date']=$done->date;
            $operation_data_supplier1['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_supplier1['sub_account']=0;
            $operation_data_supplier1['type']='supplier';
            $operation_data_supplier1['source_id']=$data['supplier_id'];
            $operation_data_supplier1['invoice_id']=$done->id;
            $operation_data_supplier1['company_id']=$company_id;


            Receipt::query()->create($receipt_data_supplier1);
            AccountOperation::query()->create($operation_data_supplier1);



            $receipt_data['amount']=$done->payment_amount;
            $receipt_data['date']=$done->date;
            $receipt_data['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['main_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['type_bonds']='expenses';
            $receipt_data['invoice_id']=$done->id;
            $receipt_data['company_id']=$company_id;


            ////

            $operation_data['creditor']=$done->payment_amount;
            $operation_data['debtor']=0;
            $operation_data['date']=$done->date;
            $operation_data['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $operation_data['source_id']=$done->id;
            $operation_data['invoice_id']=$done->id;
            $operation_data['company_id']=$company_id;


            Receipt::query()->create($receipt_data);
            AccountOperation::query()->create($operation_data);


        }elseif ($done->getRawOriginal('payment_status')=='partly_paid'){




            $receipt_data_supplier['amount']=$total_taxable+$tax_price;
            $receipt_data_supplier['date']=$done->date;
            $receipt_data_supplier['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_supplier['sub_account']=0;
            $receipt_data_supplier['main_account']=0;
            $receipt_data_supplier['type_bonds']='supplier';
            $receipt_data_supplier['invoice_id']=$data['supplier_id'];
            $receipt_data['invoice_id']=$done->id;
            $receipt_data_supplier['company_id']=$company_id;



            ////

            $operation_data_supplier['creditor']=$total_taxable+$tax_price;
            $operation_data_supplier['debtor']=0;
            $operation_data_supplier['date']=$done->date;
            $operation_data_supplier['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_supplier['sub_account']=0;
            $operation_data_supplier['type']='supplier';
            $operation_data_supplier['source_id']=$data['supplier_id'];
            $operation_data_supplier['invoice_id']=$done->id;
            $operation_data_supplier['company_id']=$company_id;


            Receipt::query()->create($receipt_data_supplier);
            AccountOperation::query()->create($operation_data_supplier);



            $sub_account=SubAccount::query()->where('type_account','purchase')->where('company_id',$company_id)->first();

            $receipt_data_purchase['amount']=$total_taxable;
            $receipt_data_purchase['date']=$done->date;
            $receipt_data_purchase['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_purchase['sub_account']=$sub_account->id;
            $receipt_data_purchase['main_account']=$sub_account->parent_id;
            $receipt_data_purchase['type_bonds']='supplier';
            $receipt_data_purchase['invoice_id']=$data['supplier_id'];
            $receipt_data_purchase['invoice_id']=$done->id;
            $receipt_data_purchase['company_id']=$company_id;


            ////

            $operation_data_purchase['creditor']=0;
            $operation_data_purchase['debtor']=$total_taxable;
            $operation_data_purchase['date']=$done->date;
            $operation_data_purchase['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_purchase['sub_account']=$sub_account->id;
            $operation_data_purchase['source_id']=$done->id;
            $operation_data_purchase['invoice_id']=$done->id;
            $operation_data_purchase['company_id']=$company_id;






            Receipt::query()->create($receipt_data_purchase);
            AccountOperation::query()->create($operation_data_purchase);



            $sub_account=SubAccount::query()->where('type_account','tax_purchase')->where('company_id',$company_id)->first();

            $receipt_data_tax['amount']=$tax_price;
            $receipt_data_tax['date']=$done->date;
            $receipt_data_tax['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_tax['sub_account']=$sub_account->id;
            $receipt_data_tax['main_account']=$sub_account->parent_id;
            $receipt_data_tax['type_bonds']='supplier';
            $receipt_data_tax['invoice_id']=$data['supplier_id'];
            $receipt_data_tax['invoice_id']=$done->id;
            $receipt_data_tax['company_id']=$company_id;


            ////

            $operation_data_tax['creditor']=0;
            $operation_data_tax['debtor']=$tax_price;
            $operation_data_tax['date']=$done->date;
            $operation_data_tax['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_tax['sub_account']=$sub_account->id;
            $operation_data_tax['source_id']=$done->id;
            $operation_data_tax['invoice_id']=$done->id;
            $operation_data_tax['company_id']=$company_id;






            Receipt::query()->create($receipt_data_tax);
            AccountOperation::query()->create($operation_data_tax);


            //////



            $receipt_data_supplier1['amount']=$data['payment_amount'];
            $receipt_data_supplier1['date']=$done->date;
            $receipt_data_supplier1['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_supplier1['sub_account']=0;
            $receipt_data_supplier1['main_account']=0;
            $receipt_data_supplier1['type_bonds']='supplier';
            $receipt_data_supplier1['invoice_id']=$data['supplier_id'];
            $receipt_data_supplier1['invoice_id']=$done->id;
            $receipt_data_supplier1['company_id']=$company_id;



            ////

            $operation_data_supplier1['creditor']=0;
            $operation_data_supplier1['debtor']=$data['payment_amount'];
            $operation_data_supplier1['date']=$done->date;
            $operation_data_supplier1['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_supplier1['sub_account']=0;
            $operation_data_supplier1['type']='supplier';
            $operation_data_supplier1['source_id']=$data['supplier_id'];
            $operation_data_supplier1['invoice_id']=$done->id;
            $operation_data_supplier1['company_id']=$company_id;




            Receipt::query()->create($receipt_data_supplier1);
            AccountOperation::query()->create($operation_data_supplier1);



            $receipt_data['amount']=$data['payment_amount'];
            $receipt_data['date']=$done->date;
            $receipt_data['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['main_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['type_bonds']='expenses';
            $receipt_data['invoice_id']=$done->id;
            $receipt_data['company_id']=$company_id;


            ////

            $operation_data['creditor']=$data['payment_amount'];
            $operation_data['debtor']=0;
            $operation_data['date']=$done->date;
            $operation_data['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $operation_data['source_id']=$done->id;
            $operation_data['invoice_id']=$done->id;
            $operation_data['company_id']=$company_id;





            Receipt::query()->create($receipt_data);
            AccountOperation::query()->create($operation_data);



        }else{

            $receipt_data_supplier['amount']=0;
            $receipt_data_supplier['date']=$done->date;
            $receipt_data_supplier['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_supplier['sub_account']=0;
            $receipt_data_supplier['main_account']=0;
            $receipt_data_supplier['type_bonds']='supplier';
            $receipt_data_supplier['invoice_id']=$data['supplier_id'];
            $receipt_data_supplier['company_id']=$company_id;


            ////

            $operation_data_supplier['creditor']=$total_taxable+$tax_price;
            $operation_data_supplier['debtor']=0;
            $operation_data_supplier['date']=$done->date;
            $operation_data_supplier['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_supplier['sub_account']=0;
            $operation_data_supplier['type']='supplier';
            $operation_data_supplier['source_id']=$data['supplier_id'];
            $operation_data_supplier['invoice_id']=$done->id;
            $operation_data_supplier['company_id']=$company_id;



            $receipt_data['invoice_id']=$done->id;

            Receipt::query()->create($receipt_data_supplier);
            AccountOperation::query()->create($operation_data_supplier);

            $sub_account=SubAccount::query()->where('type_account','purchase')->where('company_id',$company_id)->first();

            $receipt_data_purchase['amount']=$total_taxable;
            $receipt_data_purchase['date']=$done->date;
            $receipt_data_purchase['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_purchase['sub_account']=$sub_account->id;
            $receipt_data_purchase['main_account']=$sub_account->parent_id;
            $receipt_data_purchase['type_bonds']='supplier';
            $receipt_data_purchase['invoice_id']=$data['supplier_id'];
            $receipt_data_purchase['invoice_id']=$done->id;
            $receipt_data_purchase['company_id']=$company_id;


            ////

            $operation_data_purchase['creditor']=0;
            $operation_data_purchase['debtor']=$total_taxable;
            $operation_data_purchase['date']=$done->date;
            $operation_data_purchase['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_purchase['sub_account']=$sub_account->id;
            $operation_data_purchase['source_id']=$done->id;
            $operation_data_purchase['invoice_id']=$done->id;
            $operation_data_purchase['company_id']=$company_id;






            Receipt::query()->create($receipt_data_purchase);
            AccountOperation::query()->create($operation_data_purchase);



            $sub_account=SubAccount::query()->where('type_account','tax_purchase')->where('company_id',$company_id)->first();

            $receipt_data_tax['amount']=$tax_price;
            $receipt_data_tax['date']=$done->date;
            $receipt_data_tax['details']='فاتورة مشتريات رقم _'.$done->id;
            $receipt_data_tax['sub_account']=$sub_account->id;
            $receipt_data_tax['main_account']=$sub_account->parent_id;
            $receipt_data_tax['type_bonds']='supplier';
            $receipt_data_tax['invoice_id']=$data['supplier_id'];
            $receipt_data_tax['invoice_id']=$done->id;
            $receipt_data_tax['company_id']=$company_id;


            ////

            $operation_data_tax['creditor']=0;
            $operation_data_tax['debtor']=$tax_price;
            $operation_data_tax['date']=$done->date;
            $operation_data_tax['details']='فاتورة مشتريات رقم _'.$done->id;
            $operation_data_tax['sub_account']=$sub_account->id;
            $operation_data_tax['source_id']=$done->id;
            $operation_data_tax['invoice_id']=$done->id;
            $operation_data_tax['company_id']=$company_id;






            Receipt::query()->create($receipt_data_tax);
            AccountOperation::query()->create($operation_data_tax);





        }







        return $this->redirectWith(false, 'company.purchases.invoice', 'تمت الاضافة بنجاح');

    }
    public function add_product_to_invoice(Request $request)
    {

        $company_id=Auth::guard('company')->user()->id;

        $count = $request->count;
        $product_id = $request->product_id;
        $price = $request->price;
        $product = Product::query()->findOrFail($product_id);
        $product_price = $price*$count;

        $test_invoice_in_cart=InvoiceProductCart::query()->where('company_id',$company_id)->where('product_id',$product_id)->where('type_invoice','purchases')->first();


        if ($test_invoice_in_cart) {


            $data = response()->json([

                'errors' => 'error here',
                'product_id' => $product_id,
                'message' => 'this product is exist',

            ]);


            return  $data;



        }else{








            $data['product_id']=$product_id;
            $data['count']=$count;
            $data['price']=$price;
            $data['type_invoice']='purchases';
            $data['company_id']=$company_id;

            $cart=InvoiceProductCart::query()->create($data);


            $total_price=0;
            $results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','purchases')->get();

            foreach ($results as $result){
                $total_price+= ($result->count*$result->price);

            }


//
//            $company_tax=Auth::guard('company')->user()->tax;
//
//            if ($company_tax==1){
//
//                $tax_price=0.15*$total_price;
//
//            }else{
//
//                $tax_price=0;
//
//            }
            $net_price=$total_price;





            $page = view('company.purchase_invoices.product', ['product' => $product,'count'=> $count,'price'=>$price,'product_price'=>$product_price,'cart_id' => $cart->id])->render();
            $data = response()->json([
                'success' => true,
                'message' => 'return success',
                'errors' => 'error here',
                'count' => $count,
                'product_id' => $product_id,
                'total_price' => $total_price,
//                'tax_price' => $tax_price,
                'net_price' => $net_price,
                'page' => $page,

            ]);


            return $data;


        }














    }
    public function delete_product_from_invoice($id){

        $company_id= Auth::guard('company')->user()->id;

        $product_invoice_in_cart=InvoiceProductCart::query()->where('company_id',$company_id)->where('id',$id)->first();
        if ($product_invoice_in_cart){

            $product_invoice_in_cart->delete();

            $total_price=0;
            $results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','purchases')->get();

            foreach ($results as $result){
                $total_price+= ($result->count*$result->price);

            }



//            $company_tax=Auth::guard('company')->user()->tax;
//
//            if ($company_tax==1){
//
//                $tax_price=0.15*$total_price;
//
//            }else{
//
//                $tax_price=0;
//
//            }
            $net_price=$total_price;

            $data = response()->json([
                'success' => true,
                'message' => 'return success',
                'total_price' => $total_price,
//                'tax_price' => $tax_price,
                'net_price' => $net_price,
                'product_list' => $id,

            ]);


            return $data;

        }else{


        }


    }
    public function create_payments($id)
    {
        $this->authorize('purchases_invoice.payments');

        $company_id= Auth::guard('company')->user()->id;
        $banks=SubAccount::query()->where('parent_id',18)->where('company_id',$company_id)->get();
        $cashs=SubAccount::query()->where('parent_id',7)->where('company_id',$company_id)->get();
        $invoice= Invoice::query()->findOrFail($id);

        return view('company.purchase_invoices.payments',compact('banks','cashs','invoice'));
    }
    public function store_payments(PaymentRequest $request){

        $this->authorize('purchases_invoice.create');

        $company_id=Auth::guard('company')->user()->id;

        $data = $request->validated();

        $invoice= Invoice::query()->findOrFail($data['invoice_id']);
        if ($invoice){

            $supplier_id= $invoice->getRawOriginal('supplier_id');
        }


        if ($data['payment_method']=='cash'){

            $receipt_data['amount']=$data['payment_amount'];
            $receipt_data['date']=$data['date'];
            $receipt_data['details']='سند صرف _'.$data['invoice_id'];
            $receipt_data['sub_account']=$data['cash_sub_account_id'];
            $receipt_data['main_account']=$data['cash_sub_account_id'];
            $receipt_data['type_bonds']='expenses';
            $receipt_data['company_id']=$company_id;


            ////

            $operation_data['creditor']=$data['payment_amount'];
            $operation_data['debtor']=0;
            $operation_data['date']=$data['date'];
            $operation_data['details']='سند صرف _'.$data['invoice_id'];
            $operation_data['sub_account']=$data['cash_sub_account_id'];
            $operation_data['source_id']=$data['invoice_id'];
            $operation_data['invoice_id']=0;
            $operation_data['type_receipts']='expense_pay';
            $operation_data['company_id']=$company_id;




            $data['sub_account_id']=$data['cash_sub_account_id'];
            $sub=SubAccount::query()->where('id',$data['cash_sub_account_id'])->first();
//            $sub->update(['balance'=>$sub->balance-$data['payment_amount']]);



        }else {

            $receipt_data['amount']=$data['payment_amount'];
            $receipt_data['date']=$data['date'];
            $receipt_data['details']='سند صرف _'.$data['invoice_id'];
            $receipt_data['sub_account']=$data['bank_sub_account_id'];
            $receipt_data['main_account']=$data['bank_sub_account_id'];
            $receipt_data['type_bonds']='expenses';
            $receipt_data['company_id']=$company_id;

            ////

            $operation_data['creditor']=$data['payment_amount'];
            $operation_data['debtor']=0;
            $operation_data['date']=$data['date'];
            $operation_data['details']='سند صرف _'.$data['invoice_id'];
            $operation_data['sub_account']=$data['bank_sub_account_id'];
            $operation_data['source_id']=$data['invoice_id'];
            $operation_data['invoice_id']=0;
            $operation_data['type_receipts']='expense_pay';
            $operation_data['company_id']=$company_id;


            $data['sub_account_id']=$data['bank_sub_account_id'];
            $sub=SubAccount::query()->where('id',$data['bank_sub_account_id'])->first();
//            $sub->update(['balance'=>$sub->balance-$data['payment_amount']]);


        }


        $receipt_data['invoice_id']=$data['invoice_id'];

        Receipt::query()->create($receipt_data);
        AccountOperation::query()->create($operation_data);


        $receipt_data_supplier['amount']=$data['payment_amount'];
        $receipt_data_supplier['date']=$data['date'];
        $receipt_data_supplier['details']='سند صرف _'.$data['invoice_id'];
        $receipt_data_supplier['sub_account']=0;
        $receipt_data_supplier['main_account']=0;
        $receipt_data_supplier['type_bonds']='supplier';
        $receipt_data_supplier['invoice_id']=$supplier_id;
        $receipt_data_supplier['company_id']=$company_id;

        ////

        $operation_data_supplier['creditor']=0;
        $operation_data_supplier['debtor']=$data['payment_amount'];
        $operation_data_supplier['date']=$data['date'];
        $operation_data_supplier['details']='سند صرف _'.$data['invoice_id'];
        $operation_data_supplier['sub_account']=0;
        $operation_data_supplier['type']='supplier';
        $operation_data_supplier['source_id']=$supplier_id;
        $operation_data_supplier['invoice_id']=0;
        $operation_data_supplier['type_receipts']='expense_pay';
        $operation_data_supplier['company_id']=$company_id;




        Receipt::query()->create($receipt_data_supplier);
        AccountOperation::query()->create($operation_data_supplier);

        $invoice->update(['payment_amount'=>$invoice->payment_amount+$data['payment_amount']]);


        return $this->redirectWith(false, 'company.purchases.invoice', 'تمت الاضافة بنجاح');



    }
    public function search_products(request $request){


        $products=Product::query() ->where('name', 'like', "%$request->search%")->where('status','active')->latest()->get();
        $page = view('company.purchase_invoices.search_product', ['products' => $products])->render();
        $data= response()->json([
            'success' => true,
            'page' => $page,
            'message' => 'return success',
            'errors' => 'error here',
        ]);


        return $data;

    }
    public function detail_purchases_invoice($id){

        $this->authorize('purchases_invoice.show');

        $invoice= Invoice::query()->with('products')->findOrFail($id);
        return view('company.purchase_invoices.details',compact('invoice'));


    }

    public function print($id){

        $this->authorize('purchases_invoice.print');

        $invoice= Invoice::query()->where('id',$id)->first();
        return view('company.purchase_invoices.print',compact('invoice'));


    }
}
