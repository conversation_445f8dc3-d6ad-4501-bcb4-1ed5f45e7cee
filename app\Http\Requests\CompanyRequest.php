<?php

namespace App\Http\Requests;
use Illuminate\Foundation\Http\FormRequest;

class CompanyRequest extends FormRequest
{

    public function authorize()
    {
        return true;
    }


    public function rules()
    {
        return [

            'name' => 'required|string|unique:companies',
            'email' => 'required|email|unique:companies',
            'name_owner' => 'required|string',
            'name_manager' => 'required|string',
            'phone' => 'required|numeric|digits:10|unique:companies',
            'location' => 'required|string',
            'city' => 'required|string',
            'postal_code' => 'required|string',
            'tax_number' => 'required|numeric|digits:15',
            'password' => 'required|min:8',
            'confirm-password' => 'required|min:8|same:password',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'roles'=>'array'

        ];


    }

    public function messages()
    {
        return [
            'name.required' => 'حقل اسم المنشأة مطلوب',
            'name.string' => 'اسم المنشأة يجب أن يكون نص',
            'name.unique' => 'اسم المنشأة مستخدم بالفعل',
            
            'email.required' => 'حقل البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.unique' => 'البريد الإلكتروني مستخدم بالفعل',
            
            'name_owner.required' => 'حقل اسم المالك مطلوب',
            'name_owner.string' => 'اسم المالك يجب أن يكون نص',
            
            'name_manager.required' => 'حقل مدير المنشأة مطلوب',
            'name_manager.string' => 'اسم مدير المنشأة يجب أن يكون نص',
            
            'phone.required' => 'حقل رقم الجوال مطلوب',
            'phone.numeric' => 'رقم الجوال يجب أن يكون أرقام فقط',
            'phone.digits' => 'رقم الجوال يجب أن يكون 10 أرقام',
            'phone.unique' => 'رقم الجوال مستخدم بالفعل',
            
            'location.required' => 'حقل العنوان مطلوب',
            'location.string' => 'العنوان يجب أن يكون نص',
            
            'city.required' => 'حقل المدينة مطلوب',
            'city.string' => 'المدينة يجب أن تكون نص',
            
            'postal_code.required' => 'حقل الرمز البريدي مطلوب',
            'postal_code.string' => 'الرمز البريدي يجب أن يكون نص',
            
            'tax_number.required' => 'حقل الرقم الضريبي مطلوب',
            'tax_number.numeric' => 'الرقم الضريبي يجب أن يكون أرقام فقط',
            'tax_number.digits' => 'الرقم الضريبي يجب أن يكون 15 رقم',
            
            'password.required' => 'حقل كلمة المرور مطلوب',
            'password.min' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
            
            'confirm-password.required' => 'حقل تأكيد كلمة المرور مطلوب',
            'confirm-password.min' => 'تأكيد كلمة المرور يجب أن يكون 8 أحرف على الأقل',
            'confirm-password.same' => 'تأكيد كلمة المرور غير متطابق',
            
            'logo.image' => 'ملف الشعار يجب أن يكون صورة',
            'logo.mimes' => 'صيغة الشعار يجب أن تكون: jpeg, png, jpg, gif',
            'logo.max' => 'حجم الشعار يجب أن لا يتجاوز 2 ميجابايت',
        ];
    }
}
