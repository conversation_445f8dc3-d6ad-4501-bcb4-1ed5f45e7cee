/**
 * DataTables Print Helper
 * Comprehensive solution for printing DataTables with responsive behavior
 * Handles small screens and ensures all columns are included in print output
 */

window.DataTablesPrintHelper = {
    
    /**
     * Enhanced print function that handles responsive DataTables
     * @param {string} tableSelector - jQuery selector for the table (default: '#datatable')
     * @param {string} buttonSelector - jQuery selector for the print button (default: 'button[onclick="printSection()"]')
     * @param {object} options - Additional options
     */
    printSection: function(tableSelector = '#datatable', buttonSelector = null, options = {}) {
        const defaults = {
            loadingText: 'جاري تحميل البيانات...',
            printReadyClass: 'print-ready',
            disableResponsiveClass: 'disable-responsive',
            printDelay: 500,
            reloadAfterPrint: true
        };

        const config = Object.assign(defaults, options);

        // Set printing flag
        if (typeof window.isPrinting !== 'undefined') {
            window.isPrinting = true;
        }

        // Get elements
        const $table = $(tableSelector);

        // Try multiple selectors for the print button
        let $printBtn = $();
        if (buttonSelector) {
            $printBtn = $(buttonSelector);
        }

        // If no button found, try common selectors
        if ($printBtn.length === 0) {
            const commonSelectors = [
                'button[onclick="printSection()"]',
                'button:contains("طباعة")',
                '.btn:contains("طباعة")',
                'button.btn-success:contains("طباعة")',
                '[onclick="printSection()"]'
            ];

            for (let selector of commonSelectors) {
                $printBtn = $(selector);
                if ($printBtn.length > 0) {
                    break;
                }
            }
        }

        if ($table.length === 0) {
            console.error('DataTablesPrintHelper: Table not found with selector:', tableSelector);
            return;
        }

        // Print button is optional - we can still print without it
        if ($printBtn.length === 0) {
            console.warn('DataTablesPrintHelper: Print button not found, proceeding without button feedback');
        }
        
        // Show loading indicator (only if button found)
        let originalText = '';
        if ($printBtn.length > 0) {
            originalText = $printBtn.html();
            $printBtn.html(config.loadingText).prop('disabled', true);
        }
        
        // Get DataTable instance
        const dataTable = $table.DataTable();
        if (!dataTable) {
            console.error('DataTablesPrintHelper: DataTable instance not found');
            if ($printBtn.length > 0) {
                $printBtn.html(originalText).prop('disabled', false);
            }
            return;
        }
        
        // Store original responsive state
        const originalResponsive = dataTable.responsive;
        
        // Clear current table data
        dataTable.clear().draw();
        
        // Reload with all data
        dataTable.ajax.reload(function(json) {
            try {
                // Force all columns to be visible for printing
                DataTablesPrintHelper._prepareTableForPrint($table, dataTable, originalResponsive, config);
                
                // Trigger print after delay
                setTimeout(function() {
                    window.print();
                    
                    // Reset after printing
                    DataTablesPrintHelper._resetAfterPrint($table, dataTable, originalResponsive, $printBtn, originalText, config);
                }, config.printDelay);
                
            } catch (error) {
                console.error('DataTablesPrintHelper: Error preparing table for print:', error);
                DataTablesPrintHelper._resetAfterPrint($table, dataTable, originalResponsive, $printBtn, originalText, config);
            }
        });
    },
    
    /**
     * Prepare table for printing by showing all hidden columns
     * @private
     */
    _prepareTableForPrint: function($table, dataTable, originalResponsive, config) {
        // Add print-ready class to table
        $table.addClass(config.printReadyClass);
        
        // Handle responsive columns
        if (originalResponsive && typeof originalResponsive.hasHidden === 'function' && originalResponsive.hasHidden()) {
            // Recalculate responsive layout
            originalResponsive.recalc();
            
            // Force show all hidden columns
            const hiddenColumns = dataTable.columns('.dtr-hidden');
            if (hiddenColumns.nodes().length > 0) {
                hiddenColumns.nodes().to$().removeClass('dtr-hidden').show();
            }
            
            // Add disable responsive class
            $table.addClass(config.disableResponsiveClass);
        }
        
        // Force visibility of all table elements
        $table.find('th, td').each(function() {
            const $cell = $(this);
            if ($cell.hasClass('dtr-hidden') || $cell.is(':hidden')) {
                $cell.removeClass('dtr-hidden').show().css({
                    'display': 'table-cell',
                    'visibility': 'visible',
                    'opacity': '1'
                });
            }
        });
        
        // Hide responsive controls
        $table.find('.dtr-control, .dtr-details').hide();
        
        // Override any Bootstrap responsive classes
        $table.find('.d-none, .d-sm-none, .d-md-none, .d-lg-none, .d-xl-none, .d-xxl-none').each(function() {
            $(this).css({
                'display': 'table-cell !important',
                'visibility': 'visible !important'
            });
        });
    },
    
    /**
     * Reset table state after printing
     * @private
     */
    _resetAfterPrint: function($table, dataTable, originalResponsive, $printBtn, originalText, config) {
        // Reset button state (only if button exists)
        if ($printBtn.length > 0) {
            $printBtn.html(originalText).prop('disabled', false);
        }

        // Reset printing flag
        if (typeof window.isPrinting !== 'undefined') {
            window.isPrinting = false;
        }

        // Remove print classes
        $table.removeClass(config.printReadyClass + ' ' + config.disableResponsiveClass);

        // Reload normal pagination after printing
        if (config.reloadAfterPrint) {
            dataTable.ajax.reload(function() {
                // Restore responsive behavior
                if (originalResponsive && typeof originalResponsive.recalc === 'function') {
                    originalResponsive.recalc();
                }
            });
        } else {
            // Just restore responsive behavior without reloading
            if (originalResponsive && typeof originalResponsive.recalc === 'function') {
                originalResponsive.recalc();
            }
        }
    },
    
    /**
     * Initialize print functionality for a DataTable
     * @param {string} tableSelector - jQuery selector for the table
     * @param {string} buttonSelector - jQuery selector for the print button
     * @param {object} options - Additional options
     */
    init: function(tableSelector = '#datatable', buttonSelector = 'button[onclick="printSection()"]', options = {}) {
        $(document).ready(function() {
            $(buttonSelector).off('click.datatablesPrint').on('click.datatablesPrint', function(e) {
                e.preventDefault();
                DataTablesPrintHelper.printSection(tableSelector, buttonSelector, options);
            });
        });
    },
    
    /**
     * Global print function that can be called from onclick attributes
     * Maintains backward compatibility with existing code
     */
    globalPrintSection: function() {
        console.log('DataTablesPrintHelper: globalPrintSection called');

        // Check if we have a DataTable on the page
        if ($('#datatable').length > 0) {
            console.log('DataTablesPrintHelper: Found datatable element');
            try {
                const dataTable = $('#datatable').DataTable();
                if (dataTable) {
                    console.log('DataTablesPrintHelper: DataTable instance found, using enhanced print');
                    // Use the enhanced helper for DataTables
                    DataTablesPrintHelper.printSection();
                    return;
                }
            } catch (e) {
                console.warn('DataTablesPrintHelper: DataTable not initialized, falling back to simple print', e);
            }
        }

        console.log('DataTablesPrintHelper: Using simple print fallback');
        // Fallback to simple print for non-DataTable pages
        DataTablesPrintHelper.simplePrint();
    },

    /**
     * Simple print function for non-DataTable pages
     */
    simplePrint: function() {
        // Find print button and show loading
        const $printBtn = $('button:contains("طباعة"), .btn:contains("طباعة"), [onclick*="printSection"]').first();
        let originalText = '';

        if ($printBtn.length > 0) {
            originalText = $printBtn.html();
            $printBtn.html('جاري الطباعة...').prop('disabled', true);
        }

        // Simple print with delay
        setTimeout(function() {
            window.print();

            // Reset button
            if ($printBtn.length > 0) {
                $printBtn.html(originalText).prop('disabled', false);
            }
        }, 100);
    },

    /**
     * Responsive helper function to ensure sequence column visibility
     * @param {Object} dataTable - DataTables instance
     */
    ensureSequenceColumnVisibility: function(dataTable) {
        // Force sequence column to be visible on all screen sizes
        const sequenceColumn = dataTable.column(0);
        if (sequenceColumn) {
            sequenceColumn.visible(true);
        }

        // Add responsive priority to sequence column
        const sequenceHeader = $('#datatable th:first-child');
        if (sequenceHeader.length) {
            sequenceHeader.attr('data-priority', '1');
        }
    },

    /**
     * Mobile-specific adjustments for DataTables
     */
    applyMobileAdjustments: function() {
        if (window.innerWidth <= 768) {
            // Ensure sequence column is never hidden on mobile
            $('#datatable th:first-child, #datatable td:first-child').each(function() {
                $(this).removeClass('dtr-hidden').show();
            });

            // Add mobile-friendly classes
            $('#datatable').addClass('mobile-responsive');
        }
    },

    /**
     * Initialize responsive behavior
     */
    initResponsive: function() {
        // Apply mobile adjustments on window resize
        $(window).on('resize', function() {
            window.DataTablesPrintHelper.applyMobileAdjustments();
        });

        // Initial mobile adjustments
        this.applyMobileAdjustments();
    }
};

// Make the global print function available
window.printSection = DataTablesPrintHelper.globalPrintSection;

// Auto-initialize for common selectors
$(document).ready(function() {
    // Initialize responsive behavior immediately
    DataTablesPrintHelper.initResponsive();

    // Wait a bit for DataTables to initialize
    setTimeout(function() {
        // Find all print buttons with various selectors
        const printButtons = $([
            'button[onclick="printSection()"]',
            'button[onclick*="printSection"]',
            'button:contains("طباعة")',
            '.btn:contains("طباعة")'
        ].join(', '));

        if (printButtons.length > 0) {
            console.log('DataTablesPrintHelper: Found', printButtons.length, 'print button(s)');

            // Replace onclick handlers with our enhanced version
            printButtons.each(function() {
                const $btn = $(this);

                // Remove any existing onclick attribute
                $btn.removeAttr('onclick');

                // Add our click handler
                $btn.off('click.datatablesPrint').on('click.datatablesPrint', function(e) {
                    e.preventDefault();
                    DataTablesPrintHelper.globalPrintSection();
                });
            });
        } else {
            console.log('DataTablesPrintHelper: No print buttons found for auto-initialization');
        }

        // Ensure sequence column visibility for any existing DataTables
        if ($('#datatable').length > 0) {
            try {
                const dataTable = $('#datatable').DataTable();
                if (dataTable) {
                    DataTablesPrintHelper.ensureSequenceColumnVisibility(dataTable);
                }
            } catch (e) {
                console.log('DataTablesPrintHelper: DataTable not yet initialized');
            }
        }
    }, 1000); // Wait 1 second for page to fully load
});
