@extends('company.layout.CompanyLayout')
@section('title')
    مسير الرواتب
@endsection
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Products-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">


                    </div>
                    <!--end::Card title-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <!--begin::Table-->
                    <div class="fv-row row mb-15">
                        <form  class="row" method="get" action="{{route('company.payroll.filter')}}">
                            @csrf
                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">من تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date"  class="form-control mb-2" name="from" placeholder="من تاريخ" required>

                            </div>

                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">الى تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date"  class="form-control mb-2" name="to"  placeholder="الى ناريخ" re>
                            </div>


                            <!--begin::Action buttons-->
                            <div class="col-md-3">
                                <div class="col-md-9 offset-md-3">
                                    <!--begin::Cancel-->
                                    <!--end::Cancel-->
                                    <!--begin::Button-->
                                    <button  type="submit" class="btn btn-primary" >
                                        <span class="indicator-label">فلترة </span>
                                        <span class="indicator-progress">يرجى الانتظار...
															<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                    </button>
                                    <!--end::Button-->
                                </div>
                            </div>
                            <!--begin::Action buttons-->

                        </form>
                    </div>
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="datatable">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">
                                <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                    <input class="form-check-input" type="checkbox" data-kt-check="true" data-kt-check-target="#kt_ecommerce_products_table .form-check-input" value="1" />
                                </div>
                            </th>
                            <th class="min-w-100px">التاريخ</th>
                            <th class="min-w-100px">الموظف</th>
                            <th class="min-w-100px">صافي الراتب</th>
                            <th class="min-w-100px">انشئه</th>
                            <th class="min-w-100px">العميات</th>
                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">






                        </tbody>
                    </table>
                    <!--end::Table-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->
        </div>
        <!--end::Content container-->
    </div>




@endsection
@section('script')
    <script src="{{asset('/manager_assest/dist/assets/plugins/custom/datatables/datatables.bundle.js')}}"></script>

    <script>

        $(document).on('click', '.deleteRecord', (function () {
            var id = $(this).data("id");

            Swal.fire({
                text: "Are you sure you want to delete " + id + "?",
                icon: "warning",
                showCancelButton: !0,
                buttonsStyling: !1,
                confirmButtonText: "Yes, delete!",
                cancelButtonText: "No, cancel",
                customClass: {
                    confirmButton: "btn fw-bold btn-danger",
                    cancelButton: "btn fw-bold btn-active-light-primary"
                }
            })
                ////

                .then((result) => {

                    if (result['isConfirmed']) {

                        var url = '';
                        url = url.replace(':id', id);
                        var csrf_token = '{{csrf_token()}}';

                        $.ajax({
                            type: 'delete',
                            headers: {'X-CSRF-TOKEN': csrf_token},
                            url: url,
                            data: {_method: 'delete'},
                            success: function (response) {
                                console.log(response);
                                if (response === 'success') {
                                    table.DataTable().draw(true);

                                } else {
                                    console.log('fail');
                                }
                            },
                            error: function (e) {
                                // swal('exception', {icon: "error"});
                            }
                        });
                    }



                })

            ////



        }));







        var table = $('#datatable');

        // begin first table
        table.DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            ordering:false,
            searching: true,



            ajax: {
                url : '{{ route('company.payroll.index') }}',
                data: function (d) {
                    d.search = $("#search").val();
                    d.status = $("#status").val();
                }
            },
            columns: [
                {data: 'id', name: 'id'},
                {data: 'date', name: 'date'},
                {data: 'employee_name', name: 'employee_name'},

                {data: 'net_sal', name: 'net_sal'},
                {data: 'created_by', name: 'created_by'},
                {data: 'actions', name: 'actions'},
            ],

            columnDefs: [

                {
                    targets   : 0,
                    orderable : false,
                    searchable: false,
                    render    : function (data)
                    {
                        return `
                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                <input class="form-check-input" type="checkbox" id="checkbox_id" value="${data}" />
                            </div>`;
                    }
                },

                {
                    targets  : 1,
                    class: 'text-gray-800 fs-5 fw-bold',
                    orderable: true,
                    searchable: true,

                    render   : function (data)
                    {
                        return `${data ?? '---'}`;
                    }
                },
                {
                    targets  : 2,
                    class: 'text-gray-800 fs-5 fw-bold',
                    orderable: true,
                    searchable: false,
                    render   : function (data)
                    {
                        return `${data ?? '---'}`;
                    }
                },
                {
                    targets  : 3,
                    class: 'text-gray-800 fs-5 fw-bold',
                    orderable: true,
                    searchable: false,
                    render   : function (data)
                    {
                        return `${data ?? '---'}`;
                    }
                },
                {
                    targets  : 4,
                    class: 'text-gray-800 fs-5 fw-bold',
                    orderable: true,
                    searchable: false,
                    render   : function (data)
                    {
                        return `${data ?? '---'}`;
                    }
                },
                {
                    targets  : 5,
                    class: 'text-gray-800 fs-5 fw-bold',
                    orderable: true,
                    searchable: false,
                    render   : function (data)
                    {
                        return `${data ?? '---'}`;
                    }
                },


            ],
        });
        $('#search').keyup(function(){
            table.DataTable().draw(true);
        });



        $('#status').on('change', function() {
            table.DataTable().draw(true);

        });

    </script>

@endsection


