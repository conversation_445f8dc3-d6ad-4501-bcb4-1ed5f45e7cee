<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\SalesInvoiceRequest;
use App\Http\Requests\PaymentRequest;
use App\Models\AccountOperation;
use App\Models\Category;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\InvoiceProductCart;
use App\Models\Product;
use App\Models\Receipt;
use App\Models\SubAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Yajra\DataTables\DataTables;
use Auth;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

use Salla\ZATCA\GenerateQrCode;
use Salla\ZATCA\Tags\InvoiceDate;
use Salla\ZATCA\Tags\InvoiceTaxAmount;
use Salla\ZATCA\Tags\InvoiceTotalAmount;
use Salla\ZATCA\Tags\Seller;
use Salla\ZATCA\Tags\TaxNumber;
use Illuminate\Support\Facades\View;
use Spatie\Browsershot\Browsershot;
use PDF;
use Elibyy\TCPDF\Facades\TCPDF;



class SaleInvoiceController extends Controller
{
    public function index()
    {
        $this->authorize('sales_invoice.index');

        $company=Auth::guard('company')->user();



        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }

        $clients = Client::query()->where('company_id',$company_id)->get();


        if (request()->ajax())
        {
            $client = request()->get('client', false);
            $data = Invoice::query()->where('type_invoices','sales')->where('company_id',$company_id)->latest();

            if(!empty($client) || $client != '') {
                $data = $data->where('client_id',$client);
            }

            return DataTables::make($data)
                ->escapeColumns([])
                ->addColumn('actions', function ($row) {
                    return view('company.sale_invoices.buttons', compact('row'));
                })
                ->make();
        }





        return view('company.sale_invoices.index',compact('clients'));
    }

    public function create()
    {
        $this->authorize('sales_invoice.create');

        $company=Auth::guard('company')->user();
        if ($company->parent==0){
            $company_id=Auth::guard('company')->user()->id;
        }else{
            $company_id=Auth::guard('company')->user()->parent;
        }
        $clients=Client::query()->where('status','active')->where('company_id',$company_id)->get();
        $categories=Category::query()->where('status','active')->where('company_id',$company_id)->get();
        $products=Product::query()->where('status','active')->where('company_id',$company_id)->orWhere('type','service')->where('company_id',$company_id)->latest()->get();
        $banks=SubAccount::query()->where('parent_id',18)->where('company_id',$company_id)->get();
        $cashs=SubAccount::query()->where('parent_id',7)->where('company_id',$company_id)->get();
        $invoice_products = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->get();
        $total_price=0;

        foreach ($invoice_products as $result){
            $total_price+= ($result->count*$result->price);

        }

//        $company_tax=Auth::guard('company')->user()->tax;
//
//        if ($company_tax==1){
//
//            $tax_price=0.15*$total_price;
//        }else{
//
//            $tax_price=0;
//        }

        $net_price=$total_price;

        return view('company.sale_invoices.new_create',compact('clients','products','invoice_products','total_price','net_price','categories','banks','cashs'));
    }
    public function edit($id)
    {
        $this->authorize('sales_invoice.edit');

        $company_id=Auth::guard('company')->id();
        $invoice= Invoice::query()->where('id',$id)->where('company_id',$company_id)->first();
        $company_id=Auth::guard('company')->user()->id;
        $clients=Client::query()->where('status','active')->where('company_id',$company_id)->get();
        $categories=Category::query()->where('status','active')->where('company_id',$company_id)->get();
        $products=Product::query()->where('status','active')->where('opening_balance','>=',1)->where('company_id',$company_id)->orWhere('type','service')->where('company_id',$company_id)->latest()->get();
        $banks=SubAccount::query()->where('parent_id',18)->where('company_id',$company_id)->get();
        $cashs=SubAccount::query()->where('parent_id',7)->where('company_id',$company_id)->get();
        $invoice_products = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->get();
        $invoiceProduct = InvoiceProduct::query()->where('invoice_id',$id)->get();
        $total_price=0;

        foreach ($invoiceProduct as $result){
            $total_price+= ($result->count*$result->price);

        }

        $company_tax=$invoice->type_tax;

        if ($company_tax=='tax_15'){

            $tax_price=0.15*$total_price;
        }else{

            $tax_price=0;
        }

        $net_price=$total_price+$tax_price;

        return view('company.sale_invoices.edit',compact('invoice','clients','products','invoice_products','total_price',
            'tax_price','net_price','categories','banks','cashs','invoiceProduct'));
    }

    public function store(SalesInvoiceRequest $request)
    {

        $this->authorize('sales_invoice.create');

        $company_id=Auth::guard('company')->user()->id;

        $data = $request->validated();

        $total=0;


        $results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->get();
        $count_results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->count();
        $counts = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->sum('count');


        if ($count_results==0){

            return Redirect::back()->withErrors(['msg' => 'يجب اضاقة منتجات للفاتورة']);
        }


        foreach ($results as $result){
            $total+= ($result->count*$result->price);

        }




        if ($data['type_discount']=='percentage'){

            $discount_amount=($data['discount']/100)*$total;
        }elseif($data['type_discount']=='amount'){

            $discount_amount=$data['discount'];
        }else{

            $discount_amount=0;
        }

        $total_taxable=$total-$discount_amount;


        $company_tax=$data['type_tax'];

        if ($company_tax=='tax_15'){

            $tax_price=0.15*$total_taxable;
        }else{

            $tax_price=0;
        }

        $payment_amount=$total_taxable+$tax_price;


        if ($data['payment_status']=='paid'){

            $data['payment_amount']=$payment_amount;




        }

        if ($data['payment_method']=='cash'){

            $data['sub_account_id']=$data['cash_sub_account_id'];
//           $sub=SubAccount::query()->where('id',$data['cash_sub_account_id'])->first();
//           $sub->update(['balance'=>$sub->balance-$payment_amount]);


        }elseif($data['payment_method']=='bank'){

            $data['sub_account_id']=$data['bank_sub_account_id'];
//
//            $sub=SubAccount::query()->where('id',$data['bank_sub_account_id'])->first();
//            $sub->update(['balance'=>$sub->balance+$payment_amount]);


        }



        $data['tax_amount']=$tax_price;
        $data['discount_amount']=$discount_amount;
        $data['total']=$total;
        $data['total_taxable']=$total_taxable;
        $data['company_id']=$company_id;
        $data['created_by']=$company_id;
        $data['type_invoices']='sales';







        $done= Invoice::query()->create($data);

        $company_name=Auth::guard('company')->user()->name;
        $company_tax_number=Auth::guard('company')->user()->tax_number;
        $totle=$done->total+$done->tax_amount;

        $qrCode = GenerateQrCode::fromArray([
            new Seller($company_name), // seller name
            new TaxNumber($company_tax_number), // seller tax number
            new InvoiceDate($done->created_at), // invoice date as Zulu ISO8601 @see https://en.wikipedia.org/wiki/ISO_8601
            new InvoiceTotalAmount($totle), // invoice total amount
            new InvoiceTaxAmount($done->tax_amount) // invoice tax amount
            // .......
        ])->render();


        $count=Invoice::query()->where('type_invoices','sales')->where('company_id',$company_id)->count();
        Invoice::query()->where('id',$done->id)->first()->update(['qr_code_image'=>$qrCode,'number_code'=>'INV'.$count]);








        if ($done){

            foreach ($results as $result){

                if ($data['type_discount']=='percentage'){

                    $discount_amount_product=($data['discount']/100)*$result->price;
                }elseif($data['type_discount']=='amount'){

                    $discount_amount_product=$data['discount']/$counts;
                }else{

                    $discount_amount_product=0;
                }

                $product= Product::query()->where('id',$result->product_id)->first();

                $company_tax=$data['type_tax'];

                if ($company_tax=='tax_15'){

                    $tax_price_product=0.15*$result->price;
                }else{

                    $tax_price_product=0;
                }



                $product_data['product_id']=$result->product_id;
                $product_data['invoice_id']=$done->id;
                $product_data['code_number']=$result->product_id;
                $product_data['count']=$result->count;
                $product_data['net_count']=$result->count;
                $product_data['price']=($result->price-$discount_amount_product)+$tax_price_product;
                $product_data['net_price']=$product->selling_price;
                $product_data['total']=$result->count*($result->price-$discount_amount_product);

                InvoiceProduct::query()->create($product_data);


                if ($product->type=='product'){
                    $product->update(['opening_balance'=>$product->opening_balance-$result->count]);
                }


            }







        }

        InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->delete();



        if ($done->type=='wating'){

            return $this->redirectWith(false, 'company.sales.invoice', 'تمت الاضافة بنجاح');


        }



        if ($done->getRawOriginal('payment_status')=='paid'){




            $receipt_data_client['amount']=$done->payment_amount;
            $receipt_data_client['date']=$done->date;
            $receipt_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client['sub_account']=0;
            $receipt_data_client['main_account']=0;
            $receipt_data_client['type_bonds']='client';
            $receipt_data_client['invoice_id']=$data['client_id'];
            $receipt_data_client['invoice_id']=$done->id;
            $receipt_data_client['company_id']=$company_id;

            ////

            $operation_data_client['creditor']=0;
            $operation_data_client['debtor']=$done->payment_amount;
            $operation_data_client['date']=$done->date;
            $operation_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client['sub_account']=0;
            $operation_data_client['type']='client';
            $operation_data_client['source_id']=$data['client_id'];
            $operation_data_client['invoice_id']=$done->id;
            $operation_data_client['company_id']=$company_id;







            Receipt::query()->create($receipt_data_client);
            AccountOperation::query()->create($operation_data_client);


            $sub_account=SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();

            $receipt_data_sale['amount']=$total_taxable;
            $receipt_data_sale['date']=$done->date;
            $receipt_data_sale['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_sale['sub_account']=$sub_account->id;
            $receipt_data_sale['main_account']=$sub_account->parent_id;
            $receipt_data_sale['type_bonds']='client';
            $receipt_data_sale['invoice_id']=$data['client_id'];
            $receipt_data_sale['invoice_id']=$done->id;
            $receipt_data_sale['company_id']=$company_id;


            ////

            $operation_data_sale['creditor']=$total_taxable;
            $operation_data_sale['debtor']=0;
            $operation_data_sale['date']=$done->date;
            $operation_data_sale['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_sale['sub_account']=$sub_account->id;
            $operation_data_sale['source_id']=$done->id;
            $operation_data_sale['invoice_id']=$done->id;
            $operation_data_sale['company_id']=$company_id;






            Receipt::query()->create($receipt_data_sale);
            AccountOperation::query()->create($operation_data_sale);



            $sub_account=SubAccount::query()->where('type_account','tax_sale')->where('company_id',$company_id)->first();

            $receipt_data_tax['amount']=$tax_price;
            $receipt_data_tax['date']=$done->date;
            $receipt_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_tax['sub_account']=$sub_account->id;
            $receipt_data_tax['main_account']=$sub_account->parent_id;
            $receipt_data_tax['type_bonds']='client';
            $receipt_data_tax['invoice_id']=$data['client_id'];
            $receipt_data_tax['invoice_id']=$done->id;
            $receipt_data_tax['company_id']=$company_id;


            ////

            $operation_data_tax['creditor']=$tax_price;
            $operation_data_tax['debtor']=0;
            $operation_data_tax['date']=$done->date;
            $operation_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_tax['sub_account']=$sub_account->id;
            $operation_data_tax['source_id']=$done->id;
            $operation_data_tax['invoice_id']=$done->id;
            $operation_data_tax['company_id']=$company_id;





            Receipt::query()->create($receipt_data_tax);
            AccountOperation::query()->create($operation_data_tax);




            ///////



            $receipt_data_client1['amount']=$done->payment_amount;
            $receipt_data_client1['date']=$done->date;
            $receipt_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client1['sub_account']=0;
            $receipt_data_client1['main_account']=0;
            $receipt_data_client1['type_bonds']='client';
            $receipt_data_client1['invoice_id']=$data['client_id'];
            $receipt_data_client1['invoice_id']=$done->id;
            $receipt_data_client1['company_id']=$company_id;


            ////

            $operation_data_client1['creditor']=$done->payment_amount;
            $operation_data_client1['debtor']=0;
            $operation_data_client1['date']=$done->date;
            $operation_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client1['sub_account']=0;
            $operation_data_client1['type']='client';
            $operation_data_client1['source_id']=$data['client_id'];
            $operation_data_client1['invoice_id']=$done->id;
            $operation_data_client1['company_id']=$company_id;






            Receipt::query()->create($receipt_data_client1);
            AccountOperation::query()->create($operation_data_client1);


            $receipt_data['amount']=$done->payment_amount;
            $receipt_data['date']=$done->date;
            $receipt_data['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['main_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['type_bonds']='receipts';
            $receipt_data['invoice_id']=$done->id;
            $receipt_data['company_id']=$company_id;



            ////

            $operation_data['creditor']=0;
            $operation_data['debtor']=$done->payment_amount;
            $operation_data['date']=$done->date;
            $operation_data['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $operation_data['source_id']=$done->id;
            $operation_data['invoice_id']=$done->id;
            $operation_data['company_id']=$company_id;






            Receipt::query()->create($receipt_data);
            AccountOperation::query()->create($operation_data);







        }elseif ($done->getRawOriginal('payment_status')=='partly_paid'){



            $receipt_data_client['amount']=$total_taxable+$tax_price;
            $receipt_data_client['date']=$done->date;
            $receipt_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client['sub_account']=0;
            $receipt_data_client['main_account']=0;
            $receipt_data_client['type_bonds']='client';
            $receipt_data_client['invoice_id']=$data['client_id'];
            $receipt_data_client['company_id']=$company_id;

            ////

            $operation_data_client['creditor']=0;
            $operation_data_client['debtor']=$total_taxable+$tax_price;
            $operation_data_client['date']=$done->date;
            $operation_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client['sub_account']=0;
            $operation_data_client['type']='client';
            $operation_data_client['source_id']=$data['client_id'];
            $operation_data_client['invoice_id']=$done->id;
            $operation_data_client['company_id']=$company_id;




            $receipt_data['invoice_id']=$done->id;

            Receipt::query()->create($receipt_data_client);
            AccountOperation::query()->create($operation_data_client);



            $sub_account=SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();

            $receipt_data_sale['amount']=$total_taxable;
            $receipt_data_sale['date']=$done->date;
            $receipt_data_sale['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_sale['sub_account']=$sub_account->id;
            $receipt_data_sale['main_account']=$sub_account->parent_id;
            $receipt_data_sale['type_bonds']='client';
            $receipt_data_sale['invoice_id']=$data['client_id'];
            $receipt_data_sale['invoice_id']=$done->id;
            $receipt_data_sale['company_id']=$company_id;


            ////

            $operation_data_sale['creditor']=$total_taxable;
            $operation_data_sale['debtor']=0;
            $operation_data_sale['date']=$done->date;
            $operation_data_sale['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_sale['sub_account']=$sub_account->id;
            $operation_data_sale['source_id']=$done->id;
            $operation_data_sale['invoice_id']=$done->id;
            $operation_data_sale['company_id']=$company_id;






            Receipt::query()->create($receipt_data_sale);
            AccountOperation::query()->create($operation_data_sale);



            $sub_account=SubAccount::query()->where('type_account','tax_sale')->where('company_id',$company_id)->first();

            $receipt_data_tax['amount']=$tax_price;
            $receipt_data_tax['date']=$done->date;
            $receipt_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_tax['sub_account']=$sub_account->id;
            $receipt_data_tax['main_account']=$sub_account->parent_id;
            $receipt_data_tax['type_bonds']='client';
            $receipt_data_tax['invoice_id']=$data['client_id'];
            $receipt_data_tax['invoice_id']=$done->id;
            $receipt_data_tax['company_id']=$company_id;


            ////

            $operation_data_tax['creditor']=$tax_price;
            $operation_data_tax['debtor']=0;
            $operation_data_tax['date']=$done->date;
            $operation_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_tax['sub_account']=$sub_account->id;
            $operation_data_tax['source_id']=$done->id;
            $operation_data_tax['invoice_id']=$done->id;
            $operation_data_tax['company_id']=$company_id;






            Receipt::query()->create($receipt_data_tax);
            AccountOperation::query()->create($operation_data_tax);




            $receipt_data_client1['amount']=$data['payment_amount'];
            $receipt_data_client1['date']=$done->date;
            $receipt_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client1['sub_account']=0;
            $receipt_data_client1['main_account']=0;
            $receipt_data_client1['type_bonds']='client';
            $receipt_data_client1['invoice_id']=$data['client_id'];
            $receipt_data_client1['company_id']=$company_id;

            ////

            $operation_data_client1['creditor']=$data['payment_amount'];
            $operation_data_client1['debtor']=0;
            $operation_data_client1['date']=$done->date;
            $operation_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client1['sub_account']=0;
            $operation_data_client1['type']='client';
            $operation_data_client1['source_id']=$data['client_id'];
            $operation_data_client1['invoice_id']=$done->id;
            $operation_data_client1['company_id']=$company_id;




            Receipt::query()->create($receipt_data_client1);
            AccountOperation::query()->create($operation_data_client1);




            $receipt_data['amount']=$data['payment_amount'];
            $receipt_data['date']=$done->date;
            $receipt_data['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['main_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['type_bonds']='receipts';
            $receipt_data['invoice_id']=$done->id;
            $receipt_data['company_id']=$company_id;


            ////

            $operation_data['creditor']=0;
            $operation_data['debtor']=$data['payment_amount'];
            $operation_data['date']=$done->date;
            $operation_data['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $operation_data['source_id']=$done->id;
            $operation_data['invoice_id']=$done->id;
            $operation_data['company_id']=$company_id;





            Receipt::query()->create($receipt_data);
            AccountOperation::query()->create($operation_data);




        }else{


            $receipt_data_client['amount']=0;
            $receipt_data_client['date']=$done->date;
            $receipt_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client['sub_account']=0;
            $receipt_data_client['main_account']=0;
            $receipt_data_client['type_bonds']='client';
            $receipt_data_client['invoice_id']=$data['client_id'];
            $receipt_data_client['company_id']=$company_id;
            ////

            $operation_data_client['creditor']=0;
            $operation_data_client['debtor']=$total_taxable+$tax_price;
            $operation_data_client['date']=$done->date;
            $operation_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client['sub_account']=0;
            $operation_data_client['type']='client';
            $operation_data_client['source_id']=$data['client_id'];
            $operation_data_client['invoice_id']=$done->id;
            $operation_data_client['company_id']=$company_id;






            Receipt::query()->create($receipt_data_client);
            AccountOperation::query()->create($operation_data_client);


            $sub_account=SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();

            $receipt_data_client1['amount']=0;
            $receipt_data_client1['date']=$done->date;
            $receipt_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client1['sub_account']=0;
            $receipt_data_client1['main_account']=0;
            $receipt_data_client1['type_bonds']='receipts';
            $receipt_data_client1['invoice_id']=$done->id;
            $receipt_data_client1['company_id']=$company_id;


            ////

            $operation_data_client1['creditor']=$total_taxable;
            $operation_data_client1['debtor']=0;
            $operation_data_client1['date']=$done->date;
            $operation_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client1['sub_account']=$sub_account->id;
            $operation_data_client1['source_id']=$done->id;
            $operation_data_client1['invoice_id']=$done->id;
            $operation_data_client1['company_id']=$company_id;





            Receipt::query()->create($receipt_data_client1);
            AccountOperation::query()->create($operation_data_client1);



            $sub_account=SubAccount::query()->where('type_account','tax_sale')->where('company_id',$company_id)->first();

            $receipt_data_tax['amount']=$tax_price;
            $receipt_data_tax['date']=$done->date;
            $receipt_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_tax['sub_account']=$sub_account->id;
            $receipt_data_tax['main_account']=$sub_account->parent_id;
            $receipt_data_tax['type_bonds']='client';
            $receipt_data_tax['invoice_id']=$data['client_id'];
            $receipt_data_tax['invoice_id']=$done->id;
            $receipt_data_tax['company_id']=$company_id;


            ////

            $operation_data_tax['creditor']=$tax_price;
            $operation_data_tax['debtor']=0;
            $operation_data_tax['date']=$done->date;
            $operation_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_tax['sub_account']=$sub_account->id;
            $operation_data_tax['source_id']=$done->id;
            $operation_data_tax['invoice_id']=$done->id;
            $operation_data_tax['company_id']=$company_id;






            Receipt::query()->create($receipt_data_tax);
            AccountOperation::query()->create($operation_data_tax);

        }







        return $this->redirectWith(false, 'company.sales.invoice', 'تمت الاضافة بنجاح');

    }
    public function store_done(SalesInvoiceRequest $request,$id)
    {

        $company_id=Auth::guard('company')->user()->id;

        $data = $request->validated();


        $total=0;


        $results = InvoiceProduct::query()->where('invoice_id',$id)->get();
        $count_results = InvoiceProduct::query()->where('invoice_id',$id)->count();
        $counts = InvoiceProduct::query()->where('invoice_id',$id)->sum('net_count');


        if ($count_results==0){

            return Redirect::back()->withErrors(['msg' => 'يجب اضاقة منتجات للفاتورة']);
        }


        foreach ($results as $result){
            $total+= ($result->count*$result->price);

        }




        if ($data['type_discount']=='percentage'){

            $discount_amount=($data['discount']/100)*$total;
        }elseif($data['type_discount']=='amount'){

            $discount_amount=$data['discount'];
        }else{

            $discount_amount=0;
        }

        $total_taxable=$total-$discount_amount;


        $company_tax=$data['type_tax'];

        if ($company_tax=='tax_15'){

            $tax_price=0.15*$total_taxable;
        }else{

            $tax_price=0;
        }

        $payment_amount=$total_taxable+$tax_price;


        if ($data['payment_status']=='paid'){

            $data['payment_amount']=$payment_amount;




        }

        if ($data['payment_method']=='cash'){

            $data['sub_account_id']=$data['cash_sub_account_id'];
//           $sub=SubAccount::query()->where('id',$data['cash_sub_account_id'])->first();
//           $sub->update(['balance'=>$sub->balance-$payment_amount]);


        }elseif($data['payment_method']=='bank'){

            $data['sub_account_id']=$data['bank_sub_account_id'];
//
//            $sub=SubAccount::query()->where('id',$data['bank_sub_account_id'])->first();
//            $sub->update(['balance'=>$sub->balance+$payment_amount]);


        }



        $data['tax_amount']=$tax_price;
        $data['discount_amount']=$discount_amount;
        $data['total']=$total;
        $data['total_taxable']=$total_taxable;
        $data['company_id']=$company_id;
        $data['created_by']=$company_id;
        $data['type_invoices']='sales';





        if ($data['type']=='wating'){

            return $this->redirectWith(false, 'company.sales.invoice', 'تمت الاضافة بنجاح');


        }


        $done_update= Invoice::query()->where('id',$id)->first()->update($data);
        $done= Invoice::query()->where('id',$id)->first();





        if ($done->getRawOriginal('payment_status')=='paid'){




            $receipt_data_client['amount']=$done->payment_amount;
            $receipt_data_client['date']=$done->date;
            $receipt_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client['sub_account']=0;
            $receipt_data_client['main_account']=0;
            $receipt_data_client['type_bonds']='client';
            $receipt_data_client['invoice_id']=$data['client_id'];
            $receipt_data_client['invoice_id']=$done->id;
            $receipt_data_client['company_id']=$company_id;

            ////

            $operation_data_client['creditor']=0;
            $operation_data_client['debtor']=$done->payment_amount;
            $operation_data_client['date']=$done->date;
            $operation_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client['sub_account']=0;
            $operation_data_client['type']='client';
            $operation_data_client['source_id']=$data['client_id'];
            $operation_data_client['invoice_id']=$done->id;
            $operation_data_client['company_id']=$company_id;







            Receipt::query()->create($receipt_data_client);
            AccountOperation::query()->create($operation_data_client);


            $sub_account=SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();

            $receipt_data_sale['amount']=$total_taxable;
            $receipt_data_sale['date']=$done->date;
            $receipt_data_sale['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_sale['sub_account']=$sub_account->id;
            $receipt_data_sale['main_account']=$sub_account->parent_id;
            $receipt_data_sale['type_bonds']='client';
            $receipt_data_sale['invoice_id']=$data['client_id'];
            $receipt_data_sale['invoice_id']=$done->id;
            $receipt_data_sale['company_id']=$company_id;


            ////

            $operation_data_sale['creditor']=$total_taxable;
            $operation_data_sale['debtor']=0;
            $operation_data_sale['date']=$done->date;
            $operation_data_sale['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_sale['sub_account']=$sub_account->id;
            $operation_data_sale['source_id']=$done->id;
            $operation_data_sale['invoice_id']=$done->id;
            $operation_data_sale['company_id']=$company_id;






            Receipt::query()->create($receipt_data_sale);
            AccountOperation::query()->create($operation_data_sale);



            $sub_account=SubAccount::query()->where('type_account','tax_sale')->where('company_id',$company_id)->first();

            $receipt_data_tax['amount']=$tax_price;
            $receipt_data_tax['date']=$done->date;
            $receipt_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_tax['sub_account']=$sub_account->id;
            $receipt_data_tax['main_account']=$sub_account->parent_id;
            $receipt_data_tax['type_bonds']='client';
            $receipt_data_tax['invoice_id']=$data['client_id'];
            $receipt_data_tax['invoice_id']=$done->id;
            $receipt_data_tax['company_id']=$company_id;


            ////

            $operation_data_tax['creditor']=$tax_price;
            $operation_data_tax['debtor']=0;
            $operation_data_tax['date']=$done->date;
            $operation_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_tax['sub_account']=$sub_account->id;
            $operation_data_tax['source_id']=$done->id;
            $operation_data_tax['invoice_id']=$done->id;
            $operation_data_tax['company_id']=$company_id;





            Receipt::query()->create($receipt_data_tax);
            AccountOperation::query()->create($operation_data_tax);




            ///////



            $receipt_data_client1['amount']=$done->payment_amount;
            $receipt_data_client1['date']=$done->date;
            $receipt_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client1['sub_account']=0;
            $receipt_data_client1['main_account']=0;
            $receipt_data_client1['type_bonds']='client';
            $receipt_data_client1['invoice_id']=$data['client_id'];
            $receipt_data_client1['invoice_id']=$done->id;
            $receipt_data_client1['company_id']=$company_id;


            ////

            $operation_data_client1['creditor']=$done->payment_amount;
            $operation_data_client1['debtor']=0;
            $operation_data_client1['date']=$done->date;
            $operation_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client1['sub_account']=0;
            $operation_data_client1['type']='client';
            $operation_data_client1['source_id']=$data['client_id'];
            $operation_data_client1['invoice_id']=$done->id;
            $operation_data_client1['company_id']=$company_id;






            Receipt::query()->create($receipt_data_client1);
            AccountOperation::query()->create($operation_data_client1);


            $receipt_data['amount']=$done->payment_amount;
            $receipt_data['date']=$done->date;
            $receipt_data['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['main_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['type_bonds']='receipts';
            $receipt_data['invoice_id']=$done->id;
            $receipt_data['company_id']=$company_id;



            ////

            $operation_data['creditor']=0;
            $operation_data['debtor']=$done->payment_amount;
            $operation_data['date']=$done->date;
            $operation_data['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $operation_data['source_id']=$done->id;
            $operation_data['invoice_id']=$done->id;
            $operation_data['company_id']=$company_id;






            Receipt::query()->create($receipt_data);
            AccountOperation::query()->create($operation_data);







        }elseif ($done->getRawOriginal('payment_status')=='partly_paid'){



            $receipt_data_client['amount']=$total_taxable+$tax_price;
            $receipt_data_client['date']=$done->date;
            $receipt_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client['sub_account']=0;
            $receipt_data_client['main_account']=0;
            $receipt_data_client['type_bonds']='client';
            $receipt_data_client['invoice_id']=$data['client_id'];
            $receipt_data_client['company_id']=$company_id;

            ////

            $operation_data_client['creditor']=0;
            $operation_data_client['debtor']=$total_taxable+$tax_price;
            $operation_data_client['date']=$done->date;
            $operation_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client['sub_account']=0;
            $operation_data_client['type']='client';
            $operation_data_client['source_id']=$data['client_id'];
            $operation_data_client['invoice_id']=$done->id;
            $operation_data_client['company_id']=$company_id;




            $receipt_data['invoice_id']=$done->id;

            Receipt::query()->create($receipt_data_client);
            AccountOperation::query()->create($operation_data_client);



            $sub_account=SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();

            $receipt_data_sale['amount']=$total_taxable;
            $receipt_data_sale['date']=$done->date;
            $receipt_data_sale['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_sale['sub_account']=$sub_account->id;
            $receipt_data_sale['main_account']=$sub_account->parent_id;
            $receipt_data_sale['type_bonds']='client';
            $receipt_data_sale['invoice_id']=$data['client_id'];
            $receipt_data_sale['invoice_id']=$done->id;
            $receipt_data_sale['company_id']=$company_id;


            ////

            $operation_data_sale['creditor']=$total_taxable;
            $operation_data_sale['debtor']=0;
            $operation_data_sale['date']=$done->date;
            $operation_data_sale['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_sale['sub_account']=$sub_account->id;
            $operation_data_sale['source_id']=$done->id;
            $operation_data_sale['invoice_id']=$done->id;
            $operation_data_sale['company_id']=$company_id;






            Receipt::query()->create($receipt_data_sale);
            AccountOperation::query()->create($operation_data_sale);



            $sub_account=SubAccount::query()->where('type_account','tax_sale')->where('company_id',$company_id)->first();

            $receipt_data_tax['amount']=$tax_price;
            $receipt_data_tax['date']=$done->date;
            $receipt_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_tax['sub_account']=$sub_account->id;
            $receipt_data_tax['main_account']=$sub_account->parent_id;
            $receipt_data_tax['type_bonds']='client';
            $receipt_data_tax['invoice_id']=$data['client_id'];
            $receipt_data_tax['invoice_id']=$done->id;
            $receipt_data_tax['company_id']=$company_id;


            ////

            $operation_data_tax['creditor']=$tax_price;
            $operation_data_tax['debtor']=0;
            $operation_data_tax['date']=$done->date;
            $operation_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_tax['sub_account']=$sub_account->id;
            $operation_data_tax['source_id']=$done->id;
            $operation_data_tax['invoice_id']=$done->id;
            $operation_data_tax['company_id']=$company_id;






            Receipt::query()->create($receipt_data_tax);
            AccountOperation::query()->create($operation_data_tax);




            $receipt_data_client1['amount']=$data['payment_amount'];
            $receipt_data_client1['date']=$done->date;
            $receipt_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client1['sub_account']=0;
            $receipt_data_client1['main_account']=0;
            $receipt_data_client1['type_bonds']='client';
            $receipt_data_client1['invoice_id']=$data['client_id'];
            $receipt_data_client1['company_id']=$company_id;

            ////

            $operation_data_client1['creditor']=$data['payment_amount'];
            $operation_data_client1['debtor']=0;
            $operation_data_client1['date']=$done->date;
            $operation_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client1['sub_account']=0;
            $operation_data_client1['type']='client';
            $operation_data_client1['source_id']=$data['client_id'];
            $operation_data_client1['invoice_id']=$done->id;
            $operation_data_client1['company_id']=$company_id;




            Receipt::query()->create($receipt_data_client1);
            AccountOperation::query()->create($operation_data_client1);




            $receipt_data['amount']=$data['payment_amount'];
            $receipt_data['date']=$done->date;
            $receipt_data['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['main_account']=$done->getRawOriginal('sub_account_id');
            $receipt_data['type_bonds']='receipts';
            $receipt_data['invoice_id']=$done->id;
            $receipt_data['company_id']=$company_id;


            ////

            $operation_data['creditor']=0;
            $operation_data['debtor']=$data['payment_amount'];
            $operation_data['date']=$done->date;
            $operation_data['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data['sub_account']=$done->getRawOriginal('sub_account_id');
            $operation_data['source_id']=$done->id;
            $operation_data['invoice_id']=$done->id;
            $operation_data['company_id']=$company_id;





            Receipt::query()->create($receipt_data);
            AccountOperation::query()->create($operation_data);




        }else{


            $receipt_data_client['amount']=0;
            $receipt_data_client['date']=$done->date;
            $receipt_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client['sub_account']=0;
            $receipt_data_client['main_account']=0;
            $receipt_data_client['type_bonds']='client';
            $receipt_data_client['invoice_id']=$data['client_id'];
            $receipt_data_client['company_id']=$company_id;
            ////

            $operation_data_client['creditor']=0;
            $operation_data_client['debtor']=$total_taxable+$tax_price;
            $operation_data_client['date']=$done->date;
            $operation_data_client['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client['sub_account']=0;
            $operation_data_client['type']='client';
            $operation_data_client['source_id']=$data['client_id'];
            $operation_data_client['invoice_id']=$done->id;
            $operation_data_client['company_id']=$company_id;






            Receipt::query()->create($receipt_data_client);
            AccountOperation::query()->create($operation_data_client);


            $sub_account=SubAccount::query()->where('type_account','sale')->where('company_id',$company_id)->first();

            $receipt_data_client1['amount']=0;
            $receipt_data_client1['date']=$done->date;
            $receipt_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_client1['sub_account']=0;
            $receipt_data_client1['main_account']=0;
            $receipt_data_client1['type_bonds']='receipts';
            $receipt_data_client1['invoice_id']=$done->id;
            $receipt_data_client1['company_id']=$company_id;


            ////

            $operation_data_client1['creditor']=$total_taxable;
            $operation_data_client1['debtor']=0;
            $operation_data_client1['date']=$done->date;
            $operation_data_client1['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_client1['sub_account']=$sub_account->id;
            $operation_data_client1['source_id']=$done->id;
            $operation_data_client1['invoice_id']=$done->id;
            $operation_data_client1['company_id']=$company_id;





            Receipt::query()->create($receipt_data_client1);
            AccountOperation::query()->create($operation_data_client1);



            $sub_account=SubAccount::query()->where('type_account','tax_sale')->where('company_id',$company_id)->first();

            $receipt_data_tax['amount']=$tax_price;
            $receipt_data_tax['date']=$done->date;
            $receipt_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $receipt_data_tax['sub_account']=$sub_account->id;
            $receipt_data_tax['main_account']=$sub_account->parent_id;
            $receipt_data_tax['type_bonds']='client';
            $receipt_data_tax['invoice_id']=$data['client_id'];
            $receipt_data_tax['invoice_id']=$done->id;
            $receipt_data_tax['company_id']=$company_id;


            ////

            $operation_data_tax['creditor']=$tax_price;
            $operation_data_tax['debtor']=0;
            $operation_data_tax['date']=$done->date;
            $operation_data_tax['details']='فاتورة مبيعات رقم _'.$done->id;
            $operation_data_tax['sub_account']=$sub_account->id;
            $operation_data_tax['source_id']=$done->id;
            $operation_data_tax['invoice_id']=$done->id;
            $operation_data_tax['company_id']=$company_id;






            Receipt::query()->create($receipt_data_tax);
            AccountOperation::query()->create($operation_data_tax);

        }







        return $this->redirectWith(false, 'company.sales.invoice', 'تمت الاضافة بنجاح');

    }
    public function add_product_to_invoice(Request $request)
    {

        $company_id=Auth::guard('company')->user()->id;

        $count = $request->count;
        $product_id = $request->product_id;
        $price = $request->price;
        $product = Product::query()->findOrFail($product_id);
        $product_price = $price*$count;

        $test_invoice_in_cart=InvoiceProductCart::query()->where('company_id',$company_id)->where('product_id',$product_id)->where('type_invoice','sales')->first();

        if ($count > $product->opening_balance){

            return  response()->json(['message'=>'count_low']);
        }

        if ($test_invoice_in_cart) {


            $data = response()->json([

                'errors' => 'error here',
                'product_id' => $product_id,
                'message' => 'this product is exist',

            ]);


            return  $data;



        }else{








            $data['product_id']=$product_id;
            $data['count']=$count;
            $data['price']=$price;
            $data['type_invoice']='sales';
            $data['company_id']=$company_id;

            $cart=InvoiceProductCart::query()->create($data);


            $total_price=0;
            $results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->get();

            foreach ($results as $result){
                $total_price+= ($result->count*$result->price);

            }

//
//            $company_tax=Auth::guard('company')->user()->tax;
//
//            if ($company_tax==1){
//
//                $tax_price=0.15*$total_price;
//            }else{
//
//                $tax_price=0;
//            }


            $net_price=$total_price;





            $page = view('company.sale_invoices.product', ['product' => $product,'count'=> $count,'product_price'=>$product_price,'cart_id' => $cart->id])->render();
            $data = response()->json([
                'success' => true,
                'message' => 'return success',
                'errors' => 'error here',
                'count' => $count,
                'product_id' => $product_id,
                'total_price' => $total_price,
//                'tax_price' => $tax_price,
                'net_price' => $net_price,
                'page' => $page,

            ]);


            return $data;


        }














    }
    public function delete_product_from_invoice($id){

        $company_id= Auth::guard('company')->user()->id;

        $product_invoice_in_cart=InvoiceProductCart::query()->where('company_id',$company_id)->where('id',$id)->first();
        if ($product_invoice_in_cart){

            $product_invoice_in_cart->delete();

            $total_price=0;
            $results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->get();

            foreach ($results as $result){
                $total_price+= ($result->count*$result->price);

            }



//            $company_tax=Auth::guard('company')->user()->tax;
//
//            if ($company_tax==1){
//
//                $tax_price=0.15*$total_price;
//            }else{
//
//                $tax_price=0;
//            }


            $net_price=$total_price;

            $data = response()->json([
                'success' => true,
                'message' => 'return success',
                'total_price' => $total_price,
//                'tax_price' => $tax_price,
                'net_price' => $net_price,
                'product_list' => $id,

            ]);


            return $data;

        }else{


        }


    }
    public function create_payments($id)
    {
        $this->authorize('sales_invoice.payments');

        $company_id=Auth::guard('company')->user()->id;

        $banks=SubAccount::query()->where('parent_id',18)->where('company_id',$company_id)->get();
        $cashs=SubAccount::query()->where('parent_id',7)->where('company_id',$company_id)->get();
        $invoice= Invoice::query()->with('products')->findOrFail($id);


        return view('company.sale_invoices.payments',compact('banks','cashs','invoice'));
    }
    public function store_payments(PaymentRequest $request){

        $this->authorize('sales_invoice.payments');

        $company_id=Auth::guard('company')->user()->id;


        $data = $request->validated();

        $invoice= Invoice::query()->with('products')->findOrFail($data['invoice_id']);
        if ($invoice){

            $client_id= $invoice->getRawOriginal('client_id');

        }


        if ($data['payment_method']=='cash'){

            $receipt_data['amount']=$data['payment_amount'];
            $receipt_data['date']=$data['date'];
            $receipt_data['details']='سند قبض_'.$data['invoice_id'];
            $receipt_data['sub_account']=$data['cash_sub_account_id'];
            $receipt_data['main_account']=$data['cash_sub_account_id'];
            $receipt_data['type_bonds']='receipts';
            $receipt_data['company_id']=$company_id;

            ////

            $operation_data['creditor']=0;
            $operation_data['debtor']=$data['payment_amount'];
            $operation_data['date']=$data['date'];
            $operation_data['details']='سند قبض_'.$data['invoice_id'];
            $operation_data['sub_account']=$data['cash_sub_account_id'];
            $operation_data['source_id']=$data['invoice_id'];
            $operation_data['type_receipts']='receipts';
            $operation_data['company_id']=$company_id;
            $operation_data['invoice_id']=0;



            $data['sub_account_id']=$data['cash_sub_account_id'];
//            $sub=SubAccount::query()->where('id',$data['cash_sub_account_id'])->first();
//            $sub->update(['balance'=>$sub->balance+$data['payment_amount']]);



        }else {

            $receipt_data['amount']=$data['payment_amount'];
            $receipt_data['date']=$data['date'];
            $receipt_data['details']='سند قبض_'.$data['invoice_id'];
            $receipt_data['sub_account']=$data['bank_sub_account_id'];
            $receipt_data['main_account']=$data['bank_sub_account_id'];
            $receipt_data['type_bonds']='receipts';
            $receipt_data['company_id']=$company_id;

            ////

            $operation_data['creditor']=0;
            $operation_data['debtor']=$data['payment_amount'];
            $operation_data['date']=$data['date'];
            $operation_data['details']='سند قبض_'.$data['invoice_id'];
            $operation_data['sub_account']=$data['bank_sub_account_id'];
            $operation_data['source_id']=$data['invoice_id'];
            $operation_data['type_receipts']='receipts';
            $operation_data['company_id']=$company_id;
            $operation_data['invoice_id']=0;



            $data['sub_account_id']=$data['bank_sub_account_id'];
//            $sub=SubAccount::query()->where('id',$data['bank_sub_account_id'])->first();
//            $sub->update(['balance'=>$sub->balance+$data['payment_amount']]);

        }




        $receipt_data['invoice_id']=$data['invoice_id'];

        Receipt::query()->create($receipt_data);
        AccountOperation::query()->create($operation_data);


        $receipt_data_client['amount']=$data['payment_amount'];
        $receipt_data_client['date']=$data['date'];
        $receipt_data_client['details']='سند قبض_'.$data['invoice_id'];
        $receipt_data_client['sub_account']=0;
        $receipt_data_client['main_account']=0;
        $receipt_data_client['type_bonds']='client';
        $receipt_data_client['invoice_id']=$client_id;
        $receipt_data_client['type_receipts']='receipts';

        $receipt_data_client['company_id']=$company_id;

        ////

        $operation_data_client['creditor']=$data['payment_amount'];
        $operation_data_client['debtor']=0;
        $operation_data_client['date']=$data['date'];;
        $operation_data_client['details']='سند قبض_'.$data['invoice_id'];
        $operation_data_client['sub_account']=0;
        $operation_data_client['type']='client';
        $operation_data_client['source_id']=$client_id;
        $operation_data_client['type_receipts']='receipts';
        $operation_data_client['invoice_id']=0;
        $operation_data_client['company_id']=$company_id;




        Receipt::query()->create($receipt_data_client);
        AccountOperation::query()->create($operation_data_client);


        $invoice->update(['payment_amount'=>$invoice->payment_amount+$data['payment_amount']]);



        return $this->redirectWith(false, 'company.sales.invoice', 'تمت الاضافة بنجاح');



    }
    public function search_products(request $request){

        $company_id=Auth::guard('company')->user()->id;

        $products=Product::query()->where('company_id',$company_id)->where('name', 'like', "%$request->search%")->where('status','active')->latest()->get();
        $page = view('company.sale_invoices.search_product', ['products' => $products])->render();
        $data= response()->json([
            'success' => true,
            'page' => $page,
            'message' => 'return success',
            'errors' => 'error here',
        ]);


        return $data;

    }
    public function detail_sales_invoice($id){
        $this->authorize('sales_invoice.show');
        $invoice= Invoice::query()->with('products')->findOrFail($id);


        // Render the Blade view into HTML
//        $html = View::make('company.sale_invoices.details',compact('invoice'))->render();
//
//        // Save the specific part of the view as PDF
//        $name = str_random(15) . "" . rand(1000000, 9999999) . "" . time() . "_" . rand(1000000, 9999999) . ".".'pdf';
//        Browsershot::html($html)
//            ->select('#export-section') // Select the specific part by ID
//            ->format('A4')              // Set the desired format
//            ->save("uploads/pdf/$name");






        return view('company.sale_invoices.details',compact('invoice'));


    }

    public function destroy($id)
    {
        $this->authorize('sales_invoice.delete');

        $data = Invoice::query()->findOrFail($id);
        $company_id=Auth::guard('company')->user()->id;
        if ($company_id==$data->company_id){
            $data->delete();
            return "success";
        }

        return redirect()->back()->with('m-class', 'success')->with('message', t('تم الحذف بنجاح'));
    }

    public function print($id){
        $this->authorize('sales_invoice.print');

        $invoice= Invoice::query()->where('id',$id)->first();

        return view('company.sale_invoices.print',compact('invoice'));


    }

    public function get_price_product($id){

        $product=Product::query()->where('id',$id)->first();
        if ($product){

            $data= response()->json([
                'success' => true,
                'price' => $product->selling_price,

            ]);

            return $data;
        }


    }
}
