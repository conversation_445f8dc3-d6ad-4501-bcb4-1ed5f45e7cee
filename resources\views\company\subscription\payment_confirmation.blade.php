@extends('company.layout.CompanyLayout')
@section('title')
    تأكيد الدفع
@endsection
@section('content')
    @if(Auth::user()->parent != 0)
        <div class="alert alert-danger">
            فقط الشركات الرئيسية يمكنها الوصول إلى صفحة الاشتراكات.
        </div>
        <script>
            window.location.href = "{{ route('company.home') }}";
        </script>
    @endif

    @cannot('subscription.subscribe')
        <div class="alert alert-danger">
            ليس لديك صلاحية للاشتراك في باقة.
        </div>
        <script>
            window.location.href = "{{ route('company.home') }}";
        </script>
    @endcannot

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <h2>تأكيد الدفع</h2>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-5">
                        <div class="d-flex flex-column">
                            <h4 class="mb-1 text-dark">تفاصيل الاشتراك</h4>
                            <p>أنت على وشك الاشتراك في باقة <strong>{{ $plan->name }}</strong></p>
                            <p>سعر الباقة: <strong>{{ $plan->price }} @include('components.riyal-symbol', ['size' => '1em'])</strong></p>
                            <p>مدة الاشتراك: <strong>{{ $plan->duration_days }} يوم</strong></p>

                            @if($activeSubscription)
                                @if($activeSubscription->is_trial)
                                <p>ملاحظة: أنت حالياً تستخدم الفترة التجريبية المجانية. سيبدأ اشتراكك الجديد بعد انتهاء فترتك التجريبية في {{ $activeSubscription->end_date->format('Y/m/d') }}</p>
                                @else
                                <p>ملاحظة: سيبدأ اشتراكك الجديد بعد انتهاء اشتراكك الحالي في {{ $activeSubscription->end_date->format('Y/m/d') }}</p>
                                @endif
                            @endif
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('company.subscription.select_plan') }}" class="btn btn-light">رجوع</a>
                        <form action="{{ route('company.subscription.initiate_payment') }}" method="post">
                            @csrf
                            <button type="submit" class="btn btn-primary">المتابعة للدفع</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
