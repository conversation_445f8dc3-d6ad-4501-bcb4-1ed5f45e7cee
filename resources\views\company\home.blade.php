@extends('company.layout.CompanyLayout')
@section('title')الرئيسية
@endsection
@section('content')

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-fluid">
            @if($hasSubscriptionExpiringSoon && Auth::user('company')->parent == 0 && Auth::user()->can('subscription.index'))
                <div class="alert alert-warning d-flex align-items-center p-5 mb-10">
                    <i class="ki-duotone ki-information-5 fs-2hx text-warning me-4"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i>
                    <div class="d-flex flex-column">
                        <h4 class="mb-1 text-warning">اشتراكك على وشك الانتهاء</h4>
                        <span>سينتهي اشتراكك بتاريخ {{ $activeSubscription->end_date->format('Y/m/d') }} (خلال {{ Carbon\Carbon::today()->diffInDays($activeSubscription->end_date, false) }} يوم). <a href="{{ route('company.subscription.index') }}" class="fw-bold">جدد اشتراكك</a> للاستمرار في استخدام خدماتنا.</span>
                    </div>
                </div>
            @endif

            <!-- Summary Cards -->
            @if(Auth::user('company')->parent == 0)
            <div class="dashboard-summary-row">
                <div class="dashboard-summary-card">
                    <div class="main-number">
                        <span class="number-text">{{ $total_salaries }}</span>
                        <span class="currency">
                            @include('components.riyal-symbol')
                        </span>
                    </div>
                    <div class="card-label">رواتب الموظفين</div>
                </div>
                <div class="dashboard-summary-card">
                    <div class="main-number">
                        <span class="number-text">{{ $profit_loss }}</span>
                        <span class="currency">
                            @include('components.riyal-symbol')
                        </span>
                    </div>
                    <div class="card-label">الأرباح و الخسائر</div>
                </div>
                <div class="dashboard-summary-card">
                    <div class="main-number">
                        <span class="number-text">{{ $total_expenses }}</span>
                        <span class="currency">
                            @include('components.riyal-symbol')
                        </span>
                    </div>
                    <div class="card-label">المصروفات</div>
                </div>
                <div class="dashboard-summary-card">
                    <div class="main-number">
                        <span class="number-text">{{ $total_purchases }}</span>
                        <span class="currency">
                            @include('components.riyal-symbol')
                        </span>
                    </div>
                    <div class="card-label">المشتريات</div>
                </div>
                <div class="dashboard-summary-card">
                    <div class="main-number">
                        <span class="number-text">{{ $total_sales }}</span>
                        <span class="currency">
                            @include('components.riyal-symbol')
                        </span>
                    </div>
                    <div class="card-label">المبيعات</div>
                </div>
            </div>
            @endif
            <!-- End Summary Cards -->

            <!-- Chart Containers -->
            @if(Auth::user('company')->parent == 0)
            <div class="row g-5 mb-5 dashboard-charts-row">
                {{-- <div class="col-md-6">
                    <div class="card card-flush h-md-100">
                        <div class="card-header"><h4 class="card-title" style="font-size: 1.7rem; font-weight: bold; color: #222;">المصروفات حسب النوع</h4></div>
                        <div class="card-body">
                            <style>
                            @media (max-width: 600px) {
                                #expensesPieChart {
                                    max-width: 220px !important;
                                }
                            }
                            </style>
                            <canvas id="expensesPieChart" width="220" height="220" style="display: block; margin: 0 auto;"></canvas>
                        </div>
                    </div>
                </div> --}}
                <div class="col-md-6">
                    <div class="card card-flush h-md-100">
                        <div class="card-header"><h4 class="card-title" style="font-size: 1.7rem; font-weight: bold; color: #222;">المبيعات والمشتريات حسب الشهر</h4></div>
                        <div class="card-body">
                            <div id="noDataMessage" style="display: none; text-align: center; padding: 40px; color: #6c757d;">
                                <i class="fas fa-chart-bar" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                <p style="font-size: 1.1rem; margin: 0;">لا توجد بيانات متاحة للعرض</p>
                                <p style="font-size: 0.9rem; margin: 0.5rem 0 0 0;">قم بإضافة فواتير مبيعات ومشتريات لعرض البيانات</p>
                            </div>
                            <canvas id="salesPurchasesBarChart"></canvas>
                        </div>
                    </div>
                </div>
                @if(Auth::user()->can('subscription.index'))
                <div class="col-md-6">
                    <div class="card card-flush h-md-100">
                        <div class="card-header">
                            <h4 class="card-title" style="font-size: 1.7rem; font-weight: bold; color: #222;">الباقة الشهرية</h4>
                        </div>
                        <div class="card-body d-flex flex-column justify-content-center">
                            <div class="text-center mb-4">
                                @if($activeSubscription)
                                    <div class="subscription-status mb-3">
                                        @if($activeSubscription->is_trial)
                                            <div class="status-badge trial">
                                                <i class="fas fa-clock me-2 text-white"></i>
                                                <span>تجريبي</span>
                                            </div>
                                        @else
                                            <div class="status-badge active">
                                                <i class="fas fa-check-circle me-2 text-white"></i>
                                                <span>{{ $activeSubscription->plan ? $activeSubscription->plan->name : 'نشط' }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    
                                    <div class="subscription-details">
                                        <div class="detail-item mb-2">
                                            <span class="detail-label">تاريخ الانتهاء:</span>
                                            <span class="detail-value">{{ $activeSubscription->end_date->format('Y/m/d') }}</span>
                                        </div>
                                        <div class="detail-item mb-2">
                                            <span class="detail-label">الأيام المتبقية:</span>
                                            <span class="detail-value">{{ $activeSubscription->end_date->diffInDays(now()) }} يوم</span>
                                        </div>
                                    </div>
                                @else
                                    <div class="no-subscription">
                                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                        <p class="text-muted mb-3">لا يوجد اشتراك نشط</p>
                                    </div>
                                @endif
                            </div>
                            
                            <div class="subscription-actions">
                                @if($activeSubscription)
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <a href="{{ route('company.subscription.show') }}" class="btn btn-light w-100">
                                                <i class="fas fa-info-circle me-1"></i>
                                                التفاصيل
                                            </a>
                                        </div>
                                        <div class="col-6">
                                            <a href="{{ route('company.subscription.select_plan') }}" class="btn btn-primary w-100">
                                                <i class="fas fa-edit me-1"></i>
                                                {{ $activeSubscription->is_trial ? 'اختيار باقة' : 'تغيير الباقة' }}
                                            </a>
                                        </div>
                                    </div>
                                @else
                                    <a href="{{ route('company.subscription.index') }}" class="btn btn-primary w-100">
                                        <i class="fas fa-plus me-1"></i>
                                        تفعيل الاشتراك
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <style>
                    .subscription-status {
                        margin-bottom: 1.5rem;
                    }
                    
                    .status-badge {
                        display: inline-flex;
                        align-items: center;
                        padding: 0.5rem 1rem;
                        border-radius: 50px;
                        font-weight: 600;
                        font-size: 0.9rem;
                    }
                    
                    .status-badge i {
                        font-size: 0.9rem;
                        position: relative;
                        top: -1px;
                    }
                    
                    .status-badge.active {
                        background: linear-gradient(135deg, #50cd89 0%, #3dd598 100%);
                        color: white;
                        box-shadow: 0 4px 12px rgba(80, 205, 137, 0.3);
                    }
                    
                    .status-badge.trial {
                        background: linear-gradient(135deg, #f6c23e 0%, #f4b619 100%);
                        color: white;
                        box-shadow: 0 4px 12px rgba(246, 194, 62, 0.3);
                    }
                    
                    .subscription-details {
                        background: #f8f9fa;
                        border-radius: 10px;
                        padding: 1rem;
                        margin: 1rem 0;
                    }
                    
                    .detail-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    
                    .detail-label {
                        color: #6c757d;
                        font-size: 0.9rem;
                    }
                    
                    .detail-value {
                        font-weight: 600;
                        color: #222;
                    }
                    
                    .no-subscription {
                        text-align: center;
                        padding: 2rem 0;
                    }
                    
                    .subscription-actions {
                        margin-top: auto;
                    }
                    
                    .btn {
                        border-radius: 8px;
                        font-weight: 500;
                        transition: all 0.3s ease;
                    }
                    
                    .btn:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    }
                    </style>
                </div>
                @endif
            </div>
            @endif
            <!-- End Chart Containers -->

            <!--begin::Row-->
            <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                <!--begin::Col-->
                <!--end::Col-->
                <!--begin::Col-->
                <!--end::Col-->
                <!--begin::Col-->

                <!--end::Col-->
            </div>
            <!--end::Row-->


        </div>
        <!--end::Content container-->
    </div>

@endsection

@section('script')
@if(Auth::user('company')->parent == 0)
<script>
document.addEventListener('DOMContentLoaded', function () {
    // Get real data from controller
    const chartMonths = @json($chartMonths);
    const salesData = @json($salesData);
    const purchasesData = @json($purchasesData);

    // Check if there's any real data (not all zeros)
    const hasRealData = salesData.some(value => value > 0) || purchasesData.some(value => value > 0);
    
    if (!hasRealData) {
        // Show no data message and hide chart
        document.getElementById('noDataMessage').style.display = 'block';
        document.getElementById('salesPurchasesBarChart').style.display = 'none';
    } else {
        // Show chart and hide no data message
        document.getElementById('noDataMessage').style.display = 'none';
        document.getElementById('salesPurchasesBarChart').style.display = 'block';
        
        // Bar Chart: Sales & Purchases by Month
        new Chart(document.getElementById('salesPurchasesBarChart'), {
            type: 'bar',
            data: {
                labels: chartMonths,
                datasets: [
                    {
                        label: 'المبيعات',
                        data: salesData,
                        backgroundColor: '#50cd89' // Metronic green
                    },
                    {
                        label: 'المشتريات',
                        data: purchasesData,
                        backgroundColor: '#009ef7' // Metronic blue
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { 
                    legend: { 
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                size: 12
                            },
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + new Intl.NumberFormat('en-US').format(context.parsed.y) + ' ريال';
                            }
                        }
                    }
                },
                scales: {
                    x: { 
                        grid: { display: false },
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: { 
                        beginAtZero: true,
                        ticks: {
                            font: {
                                size: 11
                            },
                            callback: function(value) {
                                return new Intl.NumberFormat('en-US').format(value) + ' ريال';
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
@endif
@endsection
