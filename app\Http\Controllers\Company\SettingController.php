<?php

namespace App\Http\Controllers\Company;

use App\Company;
use App\Http\Controllers\Controller;
use App\Models\AccountOperation;
use App\Models\Category;
use App\Models\Expense;
use App\Models\Invoice;
use App\Models\MainAccount;
use App\Models\Product;
use App\Models\Receipt;
use App\Models\Repurchase;
use App\Models\Resale;
use App\Models\Setting;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Http\Requests\SubAccountsRequest;
use Auth;

use App\Models\InvoiceProduct;
use App\Models\InvoiceProductCart;
use App\Http\Requests\CompanySettingsRequest;

class SettingController extends Controller
{


    public function settings()
    {
        $this->authorize('general_settings.index');
        $setting = Setting::query()->firstOrNew([
            'id' => 1
        ]);

        $parent=Auth::guard('company')->user()->id;
        $company = Company::query()->where('id',$parent)->first();
        return view('company.settings.edit', compact('setting','company'));
    }



    /**
     * Update the specified resource in storage.
     */


    public function updateSettings(CompanySettingsRequest $request)
    {
        $this->authorize('general_settings.edit');
        $data = $request->all();
        $setting = Setting::query()->findOrNew('1');
        if ($request->hasFile('logo'))
        {
            $data['logo'] = $this->uploadImage($request->file('logo'), 'settings');
        }
        $setting->update($data);

        // Handle company logo upload
        if ($request->hasFile('company_logo')) {
            try {
                $company = Auth::guard('company')->user();
                $logo = $request->file('company_logo');
                $logoName = time() . '_' . $logo->getClientOriginalName();
                $logoPath = 'uploads/companies/logos/' . $logoName;
                
                // Ensure directory exists
                if (!file_exists(public_path('uploads/companies/logos'))) {
                    mkdir(public_path('uploads/companies/logos'), 0755, true);
                }
                
                $logo->move(public_path('uploads/companies/logos'), $logoName);
                $company->update(['logo' => $logoPath]);
                

                
                // Add success message for logo update
                session()->flash('message', 'تم تحديث شعار الشركة بنجاح');
                session()->flash('m-class', 'success');
            } catch (\Exception $e) {
                \Log::error('Company logo upload failed: ' . $e->getMessage());
                session()->flash('message', 'فشل في تحديث الشعار');
                session()->flash('m-class', 'error');
            }
        }

        // Always update tax setting
        $parent=Auth::guard('company')->user()->id;
        $company = Company::query()->where('id',$parent)->first();
        $company->update(['tax' => $request->get('tax', 0)]);

        return $this->redirectWith(false, 'company.home', 'تم التعديل بنجاح');
    }


     public function  inat()
        {
            AccountOperation::truncate();
            Expense::truncate();
            Invoice::truncate();
            InvoiceProduct::truncate();
            InvoiceProductCart::truncate();
            Receipt::truncate();
            Repurchase::truncate();
            Resale::truncate();
                    return 'os';




        }



}
