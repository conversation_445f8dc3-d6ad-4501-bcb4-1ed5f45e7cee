@extends('company.layout.CompanyLayout')
@section('title')
    حالة الاشتراك
@endsection
@section('content')
    @if(Auth::user('company')->parent != 0)
        <div class="alert alert-danger">
            فقط الشركات الرئيسية يمكنها الوصول إلى صفحة الاشتراكات.
        </div>
        <script>
            window.location.href = "{{ route('company.home') }}";
        </script>
    @endif

    @cannot('subscription.index')
        <div class="alert alert-danger">
            ليس لديك صلاحية للوصول إلى صفحة الاشتراكات.
        </div>
        <script>
            window.location.href = "{{ route('company.home') }}";
        </script>
    @endcannot

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                <div class="col-12">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif
                </div>
            </div>

            <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                <div class="col-xl-6">
                    <div class="card card-flush h-md-100">
                        <div class="card-header">
                            <div class="card-title">
                                <h2>حالة الاشتراك</h2>
                            </div>
                        </div>
                        <div class="card-body pt-1">
                            @if($activeSubscription)
                                <div class="d-flex flex-column text-gray-600">
                                    <div class="d-flex align-items-center py-2">
                                        <span class="bullet bg-primary me-3"></span>
                                        <span class="fw-bold text-gray-600 fs-6">الحالة:
                                            <span class="badge badge-light-{{ $activeSubscription->status === 'active' ? 'success' : 'danger' }}">
                                                {{ $activeSubscription->status === 'active' ? 'نشط' : ($activeSubscription->status === 'expired' ? 'منتهي' : 'ملغي') }}
                                            </span>
                                        </span>
                                    </div>
                                    <div class="d-flex align-items-center py-2">
                                        <span class="bullet bg-primary me-3"></span>
                                        <span class="fw-bold text-gray-600 fs-6">الباقة:
                                            {{ $activeSubscription->plan ? $activeSubscription->plan->name : 'فترة تجريبية' }}
                                        </span>
                                    </div>
                                    <div class="d-flex align-items-center py-2">
                                        <span class="bullet bg-primary me-3"></span>
                                        <span class="fw-bold text-gray-600 fs-6">تاريخ البدء: {{ $activeSubscription->start_date->format('Y/m/d') }}</span>
                                    </div>
                                    <div class="d-flex align-items-center py-2">
                                        <span class="bullet bg-primary me-3"></span>
                                        <span class="fw-bold text-gray-600 fs-6">تاريخ الانتهاء: {{ $activeSubscription->end_date->format('Y/m/d') }}</span>
                                    </div>
                                    <div class="d-flex align-items-center py-2">
                                        <span class="bullet bg-primary me-3"></span>
                                        <span class="fw-bold text-gray-600 fs-6">الأيام المتبقية: {{ Carbon\Carbon::today()->diffInDays($activeSubscription->end_date, false) }}</span>
                                    </div>
                                    @if($activeSubscription->is_trial)
                                        <div class="d-flex align-items-center py-2">
                                            <span class="bullet bg-warning me-3"></span>
                                            <span class="fw-bold text-warning fs-6">هذا اشتراك تجريبي</span>
                                        </div>
                                    @endif
                                    @if($company->hasSubscriptionExpiringSoon())
                                        <div class="d-flex align-items-center py-2">
                                            <span class="bullet bg-danger me-3"></span>
                                            <span class="fw-bold text-danger fs-6">اشتراكك على وشك الانتهاء!</span>
                                        </div>
                                    @endif
                                </div>
                            @else
                                <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed p-6">
                                    <i class="ki-duotone ki-information fs-2tx text-warning me-4">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                    <div class="d-flex flex-stack flex-grow-1">
                                        <div class="fw-semibold">
                                            <h4 class="text-gray-900 fw-bold">لا يوجد اشتراك نشط</h4>
                                            <div class="fs-6 text-gray-700">ليس لديك اشتراك نشط. يرجى اختيار خطة اشتراك للاستمرار في استخدام خدماتنا.</div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                        <div class="card-footer pt-0">
                            <div class="d-flex flex-stack flex-wrap">
                                <a href="{{ route('company.subscription.show') }}" class="btn btn-light btn-active-light-primary my-1 me-2">عرض التفاصيل</a>
                                <a href="{{ route('company.subscription.select_plan') }}" class="btn btn-primary my-1">
                                    {{ $activeSubscription ? 'تغيير الباقة' : 'اختيار باقة' }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-6">
                    <div class="card card-flush h-md-100">
                        <div class="card-header">
                            <div class="card-title">
                                <h2>الباقات المتاحة</h2>
                            </div>
                        </div>
                        <div class="card-body pt-1">
                            <!-- Free Trial Information - Light Mode -->
                            <div class="card card-dashed border-primary border-2 mb-7 theme-light-show">
                                <div class="card-body d-flex align-items-center p-5">
                                    <i class="ki-duotone ki-gift fs-2x text-primary me-5">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                        <span class="path4"></span>
                                    </i>
                                    <div class="d-flex flex-column">
                                        <h4 class="text-primary mb-2">فترة تجريبية مجانية لمدة 14 يوم</h4>
                                        <p class="text-gray-700 mb-0">جميع الباقات تتضمن فترة تجريبية مجانية لمدة 14 يوم عند التسجيل لأول مرة.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Free Trial Information - Dark Mode -->
                            <div class="card card-dashed border-primary border-2 mb-7 theme-dark-show" style="background-color: #1e1e2d;">
                                <div class="card-body d-flex align-items-center p-5">
                                    <i class="ki-duotone ki-gift fs-2x text-primary me-5">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                        <span class="path4"></span>
                                    </i>
                                    <div class="d-flex flex-column">
                                        <h4 class="text-primary mb-2">فترة تجريبية مجانية لمدة 14 يوم</h4>
                                        <p class="text-white mb-0">جميع الباقات تتضمن فترة تجريبية مجانية لمدة 14 يوم عند التسجيل لأول مرة.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                                    <thead>
                                        <tr class="fw-bold text-muted">
                                            <th class="min-w-150px">الباقة</th>
                                            <th class="min-w-100px">المدة</th>
                                            <th class="min-w-100px">السعر</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($plans as $plan)
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="d-flex justify-content-start flex-column">
                                                            <a href="#" class="text-dark fw-bold text-hover-primary fs-6">{{ $plan->name }}</a>
                                                            @if($plan->description)
                                                                <span class="text-muted fw-semibold text-muted d-block fs-7">{{ $plan->description }}</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="text-dark fw-bold d-block fs-6">{{ $plan->duration_days }} يوم</span>
                                                </td>
                                                <td>
                                                    <span class="text-dark fw-bold d-block fs-6">{{ $plan->price }} @include('components.riyal-symbol', ['size' => '0.9em'])</span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
