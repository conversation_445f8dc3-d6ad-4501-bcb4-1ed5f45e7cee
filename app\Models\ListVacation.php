<?php

namespace App\Models;

use App\Company;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;



class ListVacation extends Model
{

    use HasFactory, SoftDeletes ;
    protected $guarded=[];


    protected $appends=['employee_name','balance','type_vic','create_name'];
    public function getEmployeeNameAttribute()
    {
        $employee=Company::query()->where('id',$this->employee_id)->first();
        if ($employee) {

            return $employee->name;
        }else{

            return  'لايوجد';
        }
    }
    public function getCreateNameAttribute()
    {
        $employee=Company::query()->where('id',$this->created_by)->first();
        if ($employee) {

            return $employee->name;
        }else{

            return  'لايوجد';
        }
    }

    public function getBalanceAttribute()
    {
        $employee=Company::query()->where('id',$this->employee_id)->first();
        if ($employee) {

            return (int)  $employee->annual_leave - $this->number_days;
        }else{

            return  'لايوجد';
        }
    }

    public function getTypeVicAttribute()
    {
        if ($this->type=='work_injury'){
           return'اصابة عمل'   ;
        }elseif ($this->type=='Unpaid'){
            return ' غير مدفوعة'   ;
        }elseif ($this->type=='maternity'){
            return 'امومة '   ;
        }elseif ($this->type=='paternity'){
            return 'ابوة '   ;
        }elseif ($this->type=='Annual'){
            return ' سنوية'   ;
        }elseif ($this->type=='hajj'){
            return 'حج '   ;
        }elseif ($this->type=='exams'){
            return 'دراسة '   ;
        }elseif ($this->type=='wedding'){
            return ' زواج'   ;
        }elseif ($this->type=='death'){
            return ' وفاة'   ;
        }elseif ($this->type=='widow'){
            return 'عدة '   ;
        }elseif ($this->type=='sickness'){
            return 'مرضية '   ;
        }
    }
}
