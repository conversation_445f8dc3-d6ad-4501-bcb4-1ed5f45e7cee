<?php

namespace App\Providers;

use App\Models\Setting;
use App\Helpers\CompanyHelper;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        view()->composer('*', function ($view)
        {
            $setting = Setting::query()->first();
            
            // Get company logo with fallback to system logo
            $companyLogo = CompanyHelper::getCompanyLogo();

            //...with this variable
            $view->with([
                'setting' => $setting,
                'companyLogo' => $companyLogo,
            ]);
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
