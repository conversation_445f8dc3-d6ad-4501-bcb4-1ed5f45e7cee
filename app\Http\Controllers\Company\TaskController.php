<?php

namespace App\Http\Controllers\Company;

use App\Company;
use App\Http\Controllers\Controller;
use App\Http\Requests\TaskRequest;
use App\Models\Client;
use App\Models\Task;
use Yajra\DataTables\DataTables;
use Auth;


class TaskController extends Controller
{
    public function index()
    {
        $this->authorize('tasks.index');
        $company=Auth::guard('company')->user()->id;

        if (request()->ajax())
        {
//            $data = Task::query()->where('employee_id',$company)->latest();
            $data = Task::query()->where(function ($query) use ($company) {
                $query->where('company_id', $company)
                    ->orWhere('employee_id', $company);
            })->latest();
            $search = request()->get('search', false);
            $status = request()->get('status', false);
            if(!empty($search) || $search != '') {
                $data = $data->where('name', 'like', '%' . $search. '%');
            }
            if(!empty($status) || $status != '') {
                $data = $data->where('status',$status);
            }
            return DataTables::make($data)
                ->escapeColumns([])
                ->addColumn('actions', function ($row) {
                    return view('company.tasks.buttons', compact('row'));
                })
                ->make();
        }

        return view('company.tasks.index');
    }


    public function create()
    {
        $this->authorize('tasks.create');

        $company=Auth::guard('company')->user();



        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }

        $employees= Company::query()->where('parent',$company_id)->get();
        $clients= Client::query()->where('company_id',$company_id)->get();
        return view('company.tasks.create',compact('employees','clients'));
    }


    public function store(TaskRequest $request)
    {
        $this->authorize('tasks.create');
        $data = $request->validated();
        $data['company_id']=Auth::guard('company')->user()->id;
        $data['created_id']=Auth::guard('company')->user()->id;

         if ($request->hasFile('file'))
        {
            $data['file'] = $this->uploadImage($request->file('file'), 'tasks');
        }
        Task::query()->create($data);
        return $this->redirectWith(false, 'company.tasks.index', 'تمت الاضافة بنجاح');
    }


    public function show(Task $task)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($task)
    {
        $this->authorize('tasks.edit');
        $task=Task::query()->findOrFail($task);
        $employees= Company::query()->where('parent',Auth::guard('company')->user()->id)->get();
        $clients= Client::query()->where('company_id',Auth::guard('company')->user()->id)->get();

        return view('company.tasks.edit',compact('task','employees','clients'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(TaskRequest $request, Task $task)
    {
        $this->authorize('tasks.edit');
        $data = $request->validated();
         if ($request->hasFile('file'))
        {
            $data['file'] = $this->uploadImage($request->file('file'), 'tasks');
        }
        $task->update($data);

        return $this->redirectWith(false, 'company.tasks.index', 'تم التعديل بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($task)
    {
        $this->authorize('tasks.delete');
        $data = Task::query()->findOrFail($task);
        $company_id=Auth::guard('company')->user()->id;
        if ($company_id==$data->company_id){
            $data->delete();
            return "success";
        }

        return redirect()->back()->with('m-class', 'success')->with('message', t('تم الحذف بنجاح'));
    }
}
