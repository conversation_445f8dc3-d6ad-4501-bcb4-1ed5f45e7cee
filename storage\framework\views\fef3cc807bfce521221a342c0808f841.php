<?php $__env->startSection('title'); ?>
    تقرير المنتجات
<?php $__env->stopSection(); ?>
<?php $__env->startSection('css'); ?>
    
    <link href="<?php echo e(asset('/manager_assest/dist/assets/plugins/custom/datatables/datatables.bundle.css')); ?>" rel="stylesheet" type="text/css" />

    
    <style>
        /* Report-specific styles only (if needed) */
        @media print {
            /* Page setup - landscape for more columns */
            @page {
                size: A4 landscape;
                margin: 10mm;
            }

            /* Hide non-printable elements */
            .btn:not([onclick*="printSection"]),
            .form-control,
            .form-label,
            .card-header,
            .fv-row {
                display: none !important;
            }

            /* Print header */
            #printable-section::before {
                content: "تقرير المنتجات";
                display: block;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 15px;
                padding-bottom: 8px;
                border-bottom: 2px solid #2196f3;
            }

            /* Ensure printable section is visible */
            #printable-section {
                display: block !important;
                visibility: visible !important;
            }

            /* Table styling - smaller font for more columns */
            table {
                width: 100% !important;
                border-collapse: collapse !important;
                font-size: 8px !important;
            }

            th, td {
                border: 1px solid #ddd !important;
                padding: 3px 4px !important;
                text-align: center !important;
            }

            th {
                background: #e3f2fd !important;
                font-weight: bold !important;
                color: #1976d2 !important;
            }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Products-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">

                    </div>
                    <!--end::Card title-->

                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">







































                    <!--begin::Table-->
                    <div class="row g-5 g-xl-10 mb-5 mb-xl-10">















































































                        <!--end::Col-->
                    </div>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('products_report.print')): ?>
                    <button onclick="printSection()" class="btn btn-success">طباعة</button>
                    <?php endif; ?>

                    <div id="printable-section" data-report-title="تقرير المنتجات" data-report-date="<?php echo e(now()->format('Y-m-d H:i')); ?>">
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="datatable">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">#</th>
                            <th class="min-w-100px">الاسم</th>
                            <th class="min-w-100px">اجمالي الرصيد</th>
                            <th class="min-w-100px">سعر اجمالي الرصيد</th>
                            <th class="min-w-100px">كمية المبيعات</th>
                            <th class="min-w-100px">اجمالي المبيعات</th>
                            <th class="min-w-100px">كمية المشتريات</th>
                            <th class="min-w-100px">اجمالي المشتريات</th>
                            <th class="min-w-100px">الرصيد الافتتاحي</th>
                            <th class="min-w-100px">سعر الرصيد الافتتاحي</th>
                            <th class="min-w-100px">متوسط سعر الشراء</th>
                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">






                        </tbody>
                    </table>
                    <!--end::Table-->
                    </div>
                    <!--end::Printable section-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->
        </div>
        <!--end::Content container-->
    </div>




<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
    
    <script src="<?php echo e(asset('/manager_assest/dist/assets/plugins/custom/datatables/datatables.bundle.js')); ?>"></script>

    
    <?php echo $__env->make('company.reports.components.datatable-script', [
        'route' => route('company.products_report'),
        'columns' => [
            ['data' => 'name', 'name' => 'name'],
            ['data' => 'net_opening_balance', 'name' => 'net_opening_balance'],
            ['data' => 'net_opening_balance_price', 'name' => 'net_opening_balance_price'],
            ['data' => 'sale_count', 'name' => 'sale_count'],
            ['data' => 'sum_sale_price', 'name' => 'sum_sale_price'],
            ['data' => 'pay_count', 'name' => 'pay_count'],
            ['data' => 'sum_pay_price', 'name' => 'sum_pay_price'],
            ['data' => 'opening_balance', 'name' => 'opening_balance'],
            ['data' => 'opening_balance_price', 'name' => 'opening_balance_price'],
            ['data' => 'avareg_price', 'name' => 'avareg_price']
        ],
        'additionalColumnDefs' => [
            [
                'targets' => 2,
                'render' => 'function(data, type, row) { return data || "0"; }'
            ],
            [
                'targets' => 3,
                'render' => 'function(data, type, row) { if (data && data > 0) { return parseFloat(data).toFixed(2); } return "0.00"; }'
            ],
            [
                'targets' => 4,
                'render' => 'function(data, type, row) { return data || "0"; }'
            ],
            [
                'targets' => 5,
                'render' => 'function(data, type, row) { if (data && data > 0) { return parseFloat(data).toFixed(2); } return "0.00"; }'
            ],
            [
                'targets' => 6,
                'render' => 'function(data, type, row) { return data || "0"; }'
            ],
            [
                'targets' => 7,
                'render' => 'function(data, type, row) { if (data && data > 0) { return parseFloat(data).toFixed(2); } return "0.00"; }'
            ],
            [
                'targets' => 8,
                'render' => 'function(data, type, row) { return data || "0"; }'
            ],
            [
                'targets' => 9,
                'render' => 'function(data, type, row) { if (data && data > 0) { return parseFloat(data).toFixed(2); } return "0.00"; }'
            ],
            [
                'targets' => 10,
                'render' => 'function(data, type, row) { if (data && data > 0) { return parseFloat(data).toFixed(2); } return "0.00"; }'
            ]
        ]
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>



<?php echo $__env->make('company.layout.CompanyLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\dasta\resources\views/company/reports/products_report.blade.php ENDPATH**/ ?>