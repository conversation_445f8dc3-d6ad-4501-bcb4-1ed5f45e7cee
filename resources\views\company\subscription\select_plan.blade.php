@extends('company.layout.CompanyLayout')
@section('title')
    اختيار باقة الاشتراك
@endsection
@section('content')
    @if(Auth::user('company')->parent != 0)
        <div class="alert alert-danger">
            فقط الشركات الرئيسية يمكنها الوصول إلى صفحة الاشتراكات.
        </div>
        <script>
            window.location.href = "{{ route('company.home') }}";
        </script>
    @endif

    @cannot('subscription.select_plan')
        <div class="alert alert-danger">
            ليس لديك صلاحية لاختيار باقة اشتراك.
        </div>
        <script>
            window.location.href = "{{ route('company.home') }}";
        </script>
    @endcannot

    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                <div class="col-12">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <h2>اختيار باقة الاشتراك</h2>
                    </div>
                </div>
                <div class="card-body">
                    @if($activeSubscription)
                        @if($activeSubscription->is_trial)
                            <div class="alert alert-info mb-5">
                                <div class="d-flex flex-column">
                                    <h4 class="mb-1 text-dark">أنت تستخدم الفترة التجريبية المجانية</h4>
                                    <span>استمتع بجميع مميزات النظام مجاناً لمدة 14 يوم. متبقي {{ Carbon\Carbon::today()->diffInDays($activeSubscription->end_date, false) }} يوم في فترتك التجريبية.</span>
                                </div>
                            </div>
                        @else
                            <div class="alert alert-info mb-5">
                                <div class="d-flex flex-column">
                                    <h4 class="mb-1 text-dark">لديك اشتراك نشط</h4>
                                    <span>سيظل اشتراكك الحالي نشطًا حتى {{ $activeSubscription->end_date->format('Y/m/d') }}. إذا اخترت باقة جديدة، فستبدأ بعد انتهاء اشتراكك الحالي.</span>
                                </div>
                            </div>
                        @endif
                    @endif

                    <!-- Free Trial Information - Light Mode -->
                    <div class="card card-dashed border-primary border-2 mb-8 theme-light-show">
                        <div class="card-body d-flex align-items-center p-5">
                            <i class="ki-duotone ki-gift fs-2x text-primary me-5">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                                <span class="path4"></span>
                            </i>
                            <div class="d-flex flex-column">
                                <h3 class="text-primary mb-2">فترة تجريبية مجانية لمدة 14 يوم</h3>
                                <p class="text-gray-700 mb-0">جميع الباقات تتضمن فترة تجريبية مجانية لمدة 14 يوم عند التسجيل لأول مرة.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Free Trial Information - Dark Mode -->
                    <div class="card card-dashed border-primary border-2 mb-8 theme-dark-show" style="background-color: #1e1e2d;">
                        <div class="card-body d-flex align-items-center p-5">
                            <i class="ki-duotone ki-gift fs-2x text-primary me-5">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                                <span class="path4"></span>
                            </i>
                            <div class="d-flex flex-column">
                                <h3 class="text-primary mb-2">فترة تجريبية مجانية لمدة 14 يوم</h3>
                                <p class="text-white mb-0">جميع الباقات تتضمن فترة تجريبية مجانية لمدة 14 يوم عند التسجيل لأول مرة.</p>
                            </div>
                        </div>
                    </div>

                    <div class="row g-5 g-xl-9 mb-5 mb-xl-9">
                        @foreach($plans as $plan)
                            <div class="col-md-4">
                                <div class="card card-bordered {{ $activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id ? 'border-success' : 'border-gray-300' }} h-100 hover-elevate-up shadow-sm theme-light-show">
                                    <div class="ribbon ribbon-top ribbon-vertical">
                                        <div class="ribbon-label {{ $activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id ? 'bg-success' : 'bg-primary' }}">
                                            <i class="ki-duotone {{ $activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id ? 'ki-check fs-2 text-white' : 'ki-star fs-2 text-white' }}">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </div>
                                    </div>

                                    <div class="card-header bg-light py-5">
                                        <div class="card-title d-flex flex-column">
                                            <span class="text-dark fw-bold fs-1 mb-1 text-center">{{ $plan->name }}</span>
                                            <div class="d-flex align-items-center justify-content-center">
                                                <span class="text-primary fw-bold fs-2x text-center mb-0">{{ $plan->price }} <span class="fs-5 fw-semibold text-gray-500">@include('components.riyal-symbol', ['size' => '1.2em'])</span></span>
                                                @if($activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id)
                                                    <span class="badge badge-success fs-7 ms-2">الباقة الحالية</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body d-flex flex-column p-8">
                                        <div class="mb-7">
                                            <div class="d-flex align-items-center mb-5">
                                                <span class="fw-semibold fs-6 text-gray-800 flex-grow-1">
                                                    المدة: {{ $plan->duration_days }} يوم
                                                </span>
                                                <i class="ki-duotone ki-check-circle fs-1 text-success">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                </i>
                                            </div>
                                            @if($plan->description)
                                                <div class="d-flex align-items-center mb-5">
                                                    <span class="fw-semibold fs-6 text-gray-800 flex-grow-1">
                                                        {{ $plan->description }}
                                                    </span>
                                                    <i class="ki-duotone ki-check-circle fs-1 text-success">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                    </i>
                                                </div>
                                            @endif
                                            <div class="d-flex align-items-center mb-5">
                                                <span class="fw-semibold fs-6 text-gray-800 flex-grow-1">
                                                    فترة تجريبية مجانية لمدة 14 يوم
                                                </span>
                                                <i class="ki-duotone ki-check-circle fs-1 text-success">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                </i>
                                            </div>
                                        </div>
                                        <div class="mt-auto">
                                            @if($activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id)
                                                <a href="{{ route('company.subscription.show') }}" class="btn btn-success w-100 py-3">
                                                    ادارة الاشتراك
                                                </a>
                                            @else
                                                <form action="{{ route('company.subscription.process_plan') }}" method="post" class="w-100">
                                                    @csrf
                                                    <input type="hidden" name="plan_id" value="{{ $plan->id }}">
                                                    <button type="submit" class="btn btn-primary w-100 py-3">
                                                        اختيار الباقة
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Dark mode version -->
                                <div class="card card-bordered {{ $activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id ? 'border-success' : 'border-primary' }} h-100 hover-elevate-up shadow-sm theme-dark-show" style="background-color: #1e1e2d;">
                                    <div class="ribbon ribbon-top ribbon-vertical">
                                        <div class="ribbon-label {{ $activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id ? 'bg-success' : 'bg-primary' }}">
                                            <i class="ki-duotone {{ $activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id ? 'ki-check fs-2 text-white' : 'ki-star fs-2 text-white' }}">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </div>
                                    </div>

                                    <div class="card-header py-5" style="background-color: #151521;">
                                        <div class="card-title d-flex flex-column">
                                            <span class="text-white fw-bolder fs-1 mb-1 text-center">{{ $plan->name }}</span>
                                            <div class="d-flex align-items-center justify-content-center">
                                                <span class="text-primary fw-bolder fs-2x text-center mb-0">{{ $plan->price }} <span class="fs-5 fw-semibold text-white">@include('components.riyal-symbol', ['size' => '1.2em']))</span></span>
                                                @if($activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id)
                                                    <span class="badge badge-success fs-7 ms-2">الباقة الحالية</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body d-flex flex-column p-8">
                                        <div class="mb-7">
                                            <div class="d-flex align-items-center mb-5">
                                                <span class="fw-bold fs-6 text-white flex-grow-1">
                                                    المدة: {{ $plan->duration_days }} يوم
                                                </span>
                                                <i class="ki-duotone ki-check-circle fs-1 text-success">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                </i>
                                            </div>
                                            @if($plan->description)
                                                <div class="d-flex align-items-center mb-5">
                                                    <span class="fw-bold fs-6 text-white flex-grow-1">
                                                        {{ $plan->description }}
                                                    </span>
                                                    <i class="ki-duotone ki-check-circle fs-1 text-success">
                                                        <span class="path1"></span>
                                                        <span class="path2"></span>
                                                    </i>
                                                </div>
                                            @endif
                                            <div class="d-flex align-items-center mb-5">
                                                <span class="fw-bold fs-6 text-white flex-grow-1">
                                                    فترة تجريبية مجانية لمدة 14 يوم
                                                </span>
                                                <i class="ki-duotone ki-check-circle fs-1 text-success">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                </i>
                                            </div>
                                        </div>
                                        <div class="mt-auto">
                                            @if($activeSubscription && $activeSubscription->plan && $activeSubscription->plan->id == $plan->id)
                                                <a href="{{ route('company.subscription.show') }}" class="btn btn-success w-100 py-3">
                                                    ادارة الاشتراك
                                                </a>
                                            @else
                                                <form action="{{ route('company.subscription.process_plan') }}" method="post" class="w-100">
                                                    @csrf
                                                    <input type="hidden" name="plan_id" value="{{ $plan->id }}">
                                                    <button type="submit" class="btn btn-primary w-100 py-3">
                                                        اختيار الباقة
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-end">
                        <a href="{{ route('company.subscription.index') }}" class="btn btn-light">رجوع</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
