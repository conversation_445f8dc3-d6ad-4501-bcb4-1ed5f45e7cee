<?php $__env->startSection('title'); ?>
    تقرير ميزان المراجعة العامة
<?php $__env->stopSection(); ?>
<?php $__env->startSection('css'); ?>
    <style>
        /* ========================================
           EXPERT TRIAL BALANCE PRINT STYLES
           Optimized for trial balance data presentation
           ======================================== */
        
        @media print {
            /* ========================================
               PAGE SETUP & BROWSER CONTROLS
               ======================================== */
            @page {
                size: A4 landscape;
                margin: 10mm;
                /* Remove default browser headers and footers */
                @top-center { content: ""; }
                @top-left { content: ""; }
                @top-right { content: ""; }
                @bottom-center { content: ""; }
                @bottom-left { content: ""; }
                @bottom-right { content: ""; }
            }
            
            html, body {
                height: 100%;
                margin: 0;
                padding: 0;
                overflow: hidden;
            }
            
            /* ========================================
               CONTENT VISIBILITY CONTROL
               ======================================== */
            body * {
                visibility: hidden;
            }
            
            #printable-section, 
            #printable-section * {
                visibility: visible;
            }
            
            #printable-section {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                height: 100%;
                overflow: visible;
            }
            
            /* ========================================
               TYPOGRAPHY & DIRECTION
               ======================================== */
            body {
                font-family: 'Beiruti', 'DejaVu Sans', sans-serif !important;
                direction: rtl !important;
                line-height: 1.3 !important;
            }
            
            /* ========================================
               LAYOUT COMPONENTS
               ======================================== */
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                margin: 0 !important;
                padding: 15px !important;
                background: #fff !important;
                width: 100% !important;
                height: auto !important;
            }
            
            .card-header {
                background: #f8f9fa !important;
                border-bottom: 2px solid #dee2e6 !important;
                padding: 10px 15px !important;
                margin-bottom: 15px !important;
            }
            
            /* ========================================
               HIDE NON-PRINTABLE ELEMENTS
               ======================================== */
            .btn,
            .fv-row,
            .form-control,
            .form-label {
                display: none !important;
            }
            
            /* ========================================
               TABLE STYLING - UNIFIED BLUE THEME
               ======================================== */
            table {
                width: 100% !important;
                border-collapse: collapse !important;
                margin: 10px 0 !important;
                font-size: 11px !important;
                text-align: center !important;
            }
            
            th, td {
                border: 1px solid #ddd !important;
                padding: 6px 8px !important;
                text-align: center !important;
                vertical-align: middle !important;
            }
            
            th {
                background: #e3f2fd !important;
                font-weight: bold !important;
                color: #1976d2 !important;
                font-size: 10px !important;
                border-bottom: 2px solid #2196f3 !important;
            }
            
            td {
                font-size: 10px !important;
                color: #000 !important;
            }
            
            /* ========================================
               CHECKBOX COLUMN HIDING
               ======================================== */
            th:first-child,
            td:first-child {
                display: none !important;
            }
            
            /* ========================================
               UNIFIED DATA STYLING
               ======================================== */
            .table {
                border: 1px solid #dee2e6 !important;
            }
            
            .table thead th {
                background: #e3f2fd !important;
                border-bottom: 2px solid #2196f3 !important;
                color: #1976d2 !important;
            }
            
            .table tbody tr:nth-child(even) {
                background: #f8f9fa !important;
            }
            
            .table tbody tr:hover {
                background: #e3f2fd !important;
            }
            
            /* ========================================
               AMOUNT COLUMN STYLING
               ======================================== */
            td:nth-child(4), /* مدين column */
            td:nth-child(5) { /* دائن column */
                font-weight: bold !important;
                color: #2e7d32 !important;
                text-align: center !important;
            }
            
            /* ========================================
               REPORT HEADER & FOOTER
               ======================================== */
            #printable-section::before {
                content: "ميزان المراجعة";
                display: block;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 15px;
                padding-bottom: 8px;
                border-bottom: 2px solid #2196f3;
            }
            
            #printable-section::after {
                content: "تاريخ التقرير: " attr(data-report-date);
                display: block;
                text-align: center;
                font-size: 10px;
                color: #666;
                margin-top: 15px;
                padding-top: 8px;
                border-top: 1px solid #eee;
            }
            
            /* ========================================
               PRINT-SPECIFIC ENHANCEMENTS
               ======================================== */
            .card-body {
                padding: 0 !important;
            }
            
            .align-middle {
                vertical-align: middle !important;
            }
            
            .text-start {
                text-align: center !important;
            }
            
            .text-gray-400 {
                color: #666 !important;
            }
            
            .fw-bold {
                font-weight: bold !important;
            }
            
            .fs-7 {
                font-size: 10px !important;
            }
            
            .text-uppercase {
                text-transform: none !important;
            }
            
            .gs-0 {
                gap: 0 !important;
            }
            
            .pe-2 {
                padding-right: 0 !important;
            }
            
            .min-w-100px {
                min-width: auto !important;
            }
            
            .text-gray-800 {
                color: #000 !important;
            }
            
            .fs-5 {
                font-size: 10px !important;
            }
            
            /* ========================================
               ENSURE COLOR PRINTING
               ======================================== */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            /* Note: DataTables responsive print fixes are now handled globally
               via /css/datatables-print-fix.css included in CompanyLayout.blade.php */

            /* ========================================
               REMOVE PAGINATION & PAGE BREAKS
               ======================================== */
            .pagination,
            .dataTables_paginate,
            .page-item,
            .page-link,
            .paginate_button,
            .dataTables_info,
            .dataTables_length,
            .dataTables_filter {
                display: none !important;
            }
            
            /* Prevent page breaks */
            table {
                page-break-inside: avoid !important;
            }
            
            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            /* Force single page */
            #printable-section {
                page-break-after: avoid !important;
                page-break-before: avoid !important;
            }
            
            /* ========================================
               FIX EMPTY SECOND PAGE
               ======================================== */
            .app-content,
            .app-container,
            .container-xxl {
                width: 100% !important;
                height: auto !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            
            /* Ensure content fits on one page */
            .card-flush {
                height: auto !important;
                min-height: auto !important;
            }
            
            /* ========================================
               HANDLE CONTENT OVERFLOW
               ======================================== */
            /* Allow content to flow to next page if needed */
            #printable-section {
                overflow: visible !important;
                height: auto !important;
                min-height: auto !important;
            }
            
            /* Ensure tables can break across pages if needed */
            table {
                page-break-inside: auto !important;
            }
            
            /* Allow rows to break if table is too large */
            tr {
                page-break-inside: avoid !important;
                page-break-after: auto !important;
            }
            
            /* Reduce font sizes for better fit */
            th {
                font-size: 9px !important;
                padding: 4px 6px !important;
            }
            
            td {
                font-size: 9px !important;
                padding: 4px 6px !important;
            }
            
            /* Reduce margins for more content space */
            @page {
                margin: 5mm !important;
            }
            
            /* Ensure all content is visible */
            .card {
                overflow: visible !important;
                height: auto !important;
                min-height: auto !important;
            }
            
            /* Allow content to scale down if needed */
            body {
                font-size: 90% !important;
            }
            
            /* Ensure no content is hidden */
            * {
                overflow: visible !important;
            }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-xxl">
            <!--begin::Products-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">

                    </div>
                    <!--end::Card title-->

                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <div class="fv-row row mb-15">
                        <div class="row" id="filter-form">
                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">من تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="from_date" id="from_date" class="form-control mb-2" value=""/>
                            </div>

                            <div class="col-md-1 d-flex align-items-center">
                                <label class="fs-2 form-label">الى تاريخ</label>
                            </div>
                            <div class="col-md-3">
                                <input type="date" name="to_date" id="to_date" class="form-control mb-2" value=""/>
                            </div>

                            <!--begin::Action buttons-->
                            <div class="col-md-3">
                                <div class="d-flex justify-content-start">
                                    <!--begin::Clear Filters Button-->
                                    <button type="button" id="clear-filters" class="btn btn-light">
                                        <i class="ki-duotone ki-cross fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                        مسح الفلاتر
                                    </button>
                                    <!--end::Clear Filters Button-->
                                </div>
                            </div>
                            <!--end::Action buttons-->

                        </div>

                    </div>
                    <!--begin::Table-->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('trial_balance.print')): ?>
                    <button onclick="printSection()" class="btn btn-success">طباعة</button>
                    <?php endif; ?>

                    <div id="printable-section" data-report-title="تقرير ميزان المراجعة العامة" data-report-date="<?php echo e(now()->format('Y-m-d H:i')); ?>">
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="static-table">
                        <thead>
                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">اسم الحساب</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">طبيعة الحساب</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">حركات مدينة </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">حركات دائنة</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">نهائى مدين</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">نهائي دائن</th>

                        </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">

                        <?php $__currentLoopData = $mains; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $main): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="odd">
                                <td class="dtr-control">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid">

                                    </div>
                                </td>

                                <td class="text-gray-800 fs-5 fw-bold"><?php echo e($main['name']); ?></td>
                                <td class="text-gray-800 fs-5 fw-bold"><?php echo e($main['type']=='debtor'?'مدين':"دائن"); ?></td>
                                <td class="text-gray-800 fs-5 fw-bold" id="main-debtor-<?php echo e($main['id']); ?>"><?php echo e($main['debtor']); ?></td>
                                <td class="text-gray-800 fs-5 fw-bold" id="main-creditor-<?php echo e($main['id']); ?>"><?php echo e($main['creditor']); ?></td>
                                <?php if($main['debtor'] >= $main['creditor']): ?>
                                <td class="text-gray-800 fs-5 fw-bold" id="main-net-debtor-<?php echo e($main['id']); ?>"><?php echo e($main['debtor'] - $main['creditor']); ?></td>
                                <td class="text-gray-800 fs-5 fw-bold" id="main-net-creditor-<?php echo e($main['id']); ?>">0</td>
                                <?php else: ?>

                                <td class="text-gray-800 fs-5 fw-bold" id="main-net-debtor-<?php echo e($main['id']); ?>">0</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="main-net-creditor-<?php echo e($main['id']); ?>"><?php echo e(abs($main['debtor'] - $main['creditor'])); ?></td>

                                <?php endif; ?>
                            </tr>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <tr class="odd">
                            <td class="dtr-control">
                                <div class="form-check form-check-sm form-check-custom form-check-solid">

                                </div>
                            </td>
                            <td class="text-gray-800 fs-5 fw-bold">العملاء</td>
                            <td class="text-gray-800 fs-5 fw-bold">مدين</td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-client-debtor"><?php echo e($client_debtor); ?></td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-client-creditor"><?php echo e($client_creditor); ?></td>
                            <?php if($client_debtor >= $client_creditor): ?>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-client-net-debtor"><?php echo e($client_debtor - $client_creditor); ?></td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-client-net-creditor">0</td>
                            <?php else: ?>

                                <td class="text-gray-800 fs-5 fw-bold" id="display-client-net-debtor">0</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-client-net-creditor"><?php echo e(abs($client_debtor - $client_creditor)); ?></td>

                            <?php endif; ?>




                        </tr>
                        <tr class="odd">
                            <td class="dtr-control">
                                <div class="form-check form-check-sm form-check-custom form-check-solid">

                                </div>
                            </td>
                            <td class="text-gray-800 fs-5 fw-bold">الموردين</td>
                            <td class="text-gray-800 fs-5 fw-bold">دائن</td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-debtor"><?php echo e($supplier_debtor); ?></td>
                            <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-creditor"><?php echo e($supplier_creditor); ?></td>
                            <?php if($supplier_debtor >= $supplier_creditor): ?>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-net-debtor"><?php echo e($supplier_debtor - $supplier_creditor); ?></td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-net-creditor">0</td>
                            <?php else: ?>

                                <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-net-debtor">0</td>
                                <td class="text-gray-800 fs-5 fw-bold" id="display-supplier-net-creditor"><?php echo e(abs($supplier_debtor - $supplier_creditor)); ?></td>

                            <?php endif; ?>





                        </tr>


                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                            <th class="w-10px pe-2">

                            </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;">المجموع</th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;"></th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-total-debtor"><?php echo e($debtor); ?></th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-total-creditor"><?php echo e($creditor); ?> </th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-net-debtor"><?php echo e($net_debtor); ?></th>
                            <th class="min-w-100px text-gray-800 fs-5 fw-bold" style="background-color: greenyellow;" id="display-net-creditor"><?php echo e($net_creditor); ?></th>


                        </tr>


                        </tbody>
                    </table>
                    <!--end::Table-->
                    </div>
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->
        </div>
        <!--end::Content container-->
    </div>




<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>

<script>
    // Simple print function for static table
    function printSection() {
        // Show loading indicator
        const printBtn = document.querySelector('button[onclick="printSection()"]');
        if (printBtn) {
            const originalText = printBtn.innerHTML;
            printBtn.innerHTML = 'جاري الطباعة...';
            printBtn.disabled = true;

            // Trigger print after a short delay
            setTimeout(function() {
                window.print();

                // Reset button
                printBtn.innerHTML = originalText;
                printBtn.disabled = false;
            }, 100);
        } else {
            // Fallback if button not found
            window.print();
        }
    }

    // Real-time filtering functionality
    function updateTotals() {
        const fromDate = $('#from_date').val();
        const toDate = $('#to_date').val();

        $.ajax({
            url: '<?php echo e(route('company.trial_balance')); ?>',
            method: 'GET',
            data: {
                from_date: fromDate,
                to_date: toDate
            },
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                console.log('Trial Balance Response:', response);
                // Update totals
                $('#display-total-creditor').text(response.totalCreditor);
                $('#display-total-debtor').text(response.totalDebtor);
                $('#display-net-creditor').text(response.netCreditor);
                $('#display-net-debtor').text(response.netDebtor);

                // Update client values
                $('#display-client-creditor').text(response.clientCreditor);
                $('#display-client-debtor').text(response.clientDebtor);

                // Calculate and update client net values
                var clientDebtor = parseFloat(response.clientDebtor.replace(/,/g, ''));
                var clientCreditor = parseFloat(response.clientCreditor.replace(/,/g, ''));
                if (clientDebtor >= clientCreditor) {
                    $('#display-client-net-debtor').text((clientDebtor - clientCreditor).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                    $('#display-client-net-creditor').text('0.00');
                } else {
                    $('#display-client-net-debtor').text('0.00');
                    $('#display-client-net-creditor').text(Math.abs(clientDebtor - clientCreditor).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                }

                // Update supplier values
                $('#display-supplier-creditor').text(response.supplierCreditor);
                $('#display-supplier-debtor').text(response.supplierDebtor);

                // Calculate and update supplier net values
                var supplierDebtor = parseFloat(response.supplierDebtor.replace(/,/g, ''));
                var supplierCreditor = parseFloat(response.supplierCreditor.replace(/,/g, ''));
                if (supplierDebtor >= supplierCreditor) {
                    $('#display-supplier-net-debtor').text((supplierDebtor - supplierCreditor).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                    $('#display-supplier-net-creditor').text('0.00');
                } else {
                    $('#display-supplier-net-debtor').text('0.00');
                    $('#display-supplier-net-creditor').text(Math.abs(supplierDebtor - supplierCreditor).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}));
                }

                // Update ALL main accounts (sub accounts) rows
                if (response.mainAccounts && response.mainAccounts.length > 0) {
                    response.mainAccounts.forEach(function(account) {
                        $('#main-debtor-' + account.id).text(account.debtor);
                        $('#main-creditor-' + account.id).text(account.creditor);
                        $('#main-net-debtor-' + account.id).text(account.net_debtor);
                        $('#main-net-creditor-' + account.id).text(account.net_creditor);
                    });
                }
            },
            error: function(xhr, status, error) {
                console.error('Trial Balance Error:', error);
                console.error('Response:', xhr.responseText);
            }
        });
    }

    // Auto-refresh on date changes
    $('#from_date, #to_date').on('change', function() {
        updateTotals();
    });

    // Clear all filters
    $('#clear-filters').on('click', function() {
        $('#from_date').val('');
        $('#to_date').val('');
        updateTotals();
    });
</script>

<?php $__env->stopSection(); ?>



<?php echo $__env->make('company.layout.CompanyLayout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\dasta\resources\views/company/reports/trial_balance.blade.php ENDPATH**/ ?>