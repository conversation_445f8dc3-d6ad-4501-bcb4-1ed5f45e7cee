/* ========================================
   DATATABLES RESPONSIVE PRINT FIX
   Comprehensive solution for printing DataTables on small screens
   ======================================== */

@media print {
    /* ========================================
       DATATABLES RESPONSIVE OVERRIDE FOR PRINT
       ======================================== */
    
    /* Force all DataTables columns to be visible during print */
    .dtr-hidden {
        display: table-cell !important;
        visibility: visible !important;
    }
    
    /* Override responsive breakpoints */
    .dtr-control {
        display: none !important;
    }
    
    /* Show all table columns regardless of responsive state */
    table.dataTable > thead > tr > th,
    table.dataTable > tbody > tr > td {
        display: table-cell !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* Force table to use full width and show all columns */
    table.dataTable {
        width: 100% !important;
        table-layout: auto !important;
        border-collapse: collapse !important;
    }
    
    /* Override any responsive hiding */
    .dt-responsive .dtr-hidden {
        display: table-cell !important;
        visibility: visible !important;
    }
    
    /* Ensure responsive controls are hidden */
    .dtr-details,
    .dtr-control:before {
        display: none !important;
    }
    
    /* Force visibility of all table elements */
    table.dataTable thead th,
    table.dataTable tbody td {
        display: table-cell !important;
        visibility: visible !important;
        opacity: 1 !important;
        border: 1px solid #ddd !important;
        padding: 4px 6px !important;
        text-align: center !important;
        vertical-align: middle !important;
    }
    
    /* Override Bootstrap responsive utilities */
    .d-none,
    .d-sm-none,
    .d-md-none,
    .d-lg-none,
    .d-xl-none,
    .d-xxl-none {
        display: table-cell !important;
        visibility: visible !important;
    }
    
    /* ========================================
       PRINT-READY STATE OVERRIDES
       ======================================== */
    
    /* When table has print-ready class, force all columns visible */
    .print-ready .dtr-hidden,
    .print-ready .dtr-control,
    .print-ready .d-none,
    .print-ready .d-sm-none,
    .print-ready .d-md-none,
    .print-ready .d-lg-none,
    .print-ready .d-xl-none,
    .print-ready .d-xxl-none {
        display: table-cell !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* Ensure print-ready table uses full width */
    .print-ready {
        width: 100% !important;
        table-layout: auto !important;
        border-collapse: collapse !important;
    }
    
    /* Force all cells in print-ready table to be visible */
    .print-ready th,
    .print-ready td {
        display: table-cell !important;
        visibility: visible !important;
        opacity: 1 !important;
        border: 1px solid #ddd !important;
        padding: 4px 6px !important;
    }
    
    /* ========================================
       RESPONSIVE TABLE WRAPPER FIXES
       ======================================== */
    
    /* Override table-responsive wrapper */
    .table-responsive {
        overflow: visible !important;
        width: 100% !important;
        height: auto !important;
        min-height: auto !important;
    }
    
    /* Ensure wrapper doesn't hide content */
    .dataTables_wrapper {
        overflow: visible !important;
        width: 100% !important;
    }
    
    /* ========================================
       SEQUENCE NUMBER COLUMN STYLING - RESPONSIVE
       ======================================== */

    /* Base styling for all screen sizes - ONLY for DataTables */
    table.dataTable th:first-child,
    table.dataTable td:first-child,
    table.dataTable .sequence-column {
        text-align: center !important;
        font-weight: bold !important;
        display: table-cell !important;
        visibility: visible !important;
        vertical-align: middle !important;
        padding: 8px 4px !important;
        min-width: 40px !important;
    }

    /* Desktop (1024px+) - Full width - ONLY for DataTables */
    @media screen and (min-width: 1024px) {
        table.dataTable th:first-child,
        table.dataTable td:first-child,
        table.dataTable .sequence-column {
            width: 50px !important;
            max-width: 50px !important;
        }
    }

    /* Tablet (768px-1023px) - Slightly smaller - ONLY for DataTables */
    @media screen and (min-width: 768px) and (max-width: 1023px) {
        table.dataTable th:first-child,
        table.dataTable td:first-child,
        table.dataTable .sequence-column {
            width: 45px !important;
            max-width: 45px !important;
            font-size: 13px !important;
        }
    }

    /* Mobile (320px-767px) - Compact but visible - ONLY for DataTables */
    @media screen and (max-width: 767px) {
        table.dataTable th:first-child,
        table.dataTable td:first-child,
        table.dataTable .sequence-column {
            width: 35px !important;
            max-width: 35px !important;
            font-size: 12px !important;
            padding: 6px 2px !important;
        }

        /* Ensure sequence column is never hidden on mobile - ONLY for DataTables */
        table.dataTable .dtr-hidden th:first-child,
        table.dataTable .dtr-hidden td:first-child {
            display: table-cell !important;
            visibility: visible !important;
        }
    }

    /* Extra small mobile (320px-479px) - Ultra compact - ONLY for DataTables */
    @media screen and (max-width: 479px) {
        table.dataTable th:first-child,
        table.dataTable td:first-child,
        table.dataTable .sequence-column {
            width: 30px !important;
            max-width: 30px !important;
            font-size: 11px !important;
            padding: 4px 1px !important;
        }
    }

    /* Ensure sequence numbers are preserved in print - ONLY for DataTables */
    table.dataTable .sequence-column::before {
        content: attr(data-sequence) !important;
    }

    /* Force sequence column visibility in DataTables responsive - ONLY for DataTables */
    table.dataTable .dtr-control th:first-child,
    table.dataTable .dtr-control td:first-child,
    table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child,
    table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child,
    table.dataTable > thead > tr > th:first-child,
    table.dataTable > tbody > tr > td:first-child {
        display: table-cell !important;
        visibility: visible !important;
    }

    /* Mobile-specific responsive fixes */
    @media screen and (max-width: 767px) {
        /* Prevent DataTables from hiding sequence column on mobile */
        table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > td:first-child,
        table.dataTable.dtr-inline.collapsed > tbody > tr[role="row"] > th:first-child {
            display: table-cell !important;
            visibility: visible !important;
        }

        /* Ensure table doesn't overflow on small screens */
        .dataTables_wrapper {
            overflow-x: auto !important;
            -webkit-overflow-scrolling: touch !important;
        }

        /* Adjust table layout for mobile */
        table.dataTable {
            width: 100% !important;
            min-width: 300px !important;
        }

        /* Compact spacing for mobile */
        table.dataTable th,
        table.dataTable td {
            padding: 4px 2px !important;
            font-size: 12px !important;
        }
    }

    /* Print-specific responsive fixes - ONLY for DataTables */
    @media print {
        /* Ensure sequence column is always visible in print - ONLY for DataTables */
        table.dataTable th:first-child,
        table.dataTable td:first-child,
        table.dataTable .sequence-column {
            display: table-cell !important;
            visibility: visible !important;
            width: 40px !important;
            max-width: 40px !important;
        }

        /* Prevent responsive behavior in print - ONLY for DataTables */
        table.dataTable .dtr-hidden {
            display: table-cell !important;
        }

        /* Compact print layout - ONLY for DataTables */
        table.dataTable {
            width: 100% !important;
            font-size: 10px !important;
        }
    }

    /* ========================================
       COLUMN WIDTH ADJUSTMENTS
       ======================================== */

    /* Adjust column widths for better printing */
    table.dataTable th,
    table.dataTable td {
        white-space: nowrap !important;
        overflow: visible !important;
        text-overflow: clip !important;
        max-width: none !important;
        min-width: auto !important;
    }
    
    /* Allow text wrapping for long content */
    table.dataTable td {
        word-wrap: break-word !important;
        word-break: break-word !important;
    }
    
    /* ========================================
       HIDE DATATABLES CONTROLS
       ======================================== */
    
    /* Hide pagination and other controls */
    .dataTables_paginate,
    .dataTables_info,
    .dataTables_length,
    .dataTables_filter,
    .dataTables_processing,
    .pagination,
    .page-item,
    .page-link,
    .paginate_button {
        display: none !important;
    }
    
    /* ========================================
       CHECKBOX COLUMN HANDLING
       ======================================== */
    
    /* Hide checkbox columns in print */
    th:first-child input[type="checkbox"],
    td:first-child input[type="checkbox"],
    th:first-child .form-check,
    td:first-child .form-check {
        display: none !important;
    }
    
    /* ========================================
       FONT AND STYLING ADJUSTMENTS
       ======================================== */
    
    /* Optimize font sizes for print */
    table.dataTable {
        font-size: 9px !important;
    }
    
    table.dataTable th {
        font-size: 8px !important;
        font-weight: bold !important;
        background: #e3f2fd !important;
        color: #1976d2 !important;
        border-bottom: 2px solid #2196f3 !important;
    }
    
    table.dataTable td {
        font-size: 8px !important;
        color: #000 !important;
    }
    
    /* ========================================
       PAGE BREAK HANDLING
       ======================================== */
    
    /* Prevent page breaks within table rows */
    table.dataTable tr {
        page-break-inside: avoid !important;
        page-break-after: auto !important;
    }
    
    /* Allow table to break across pages if needed */
    table.dataTable {
        page-break-inside: auto !important;
    }
    
    /* ========================================
       ENSURE COLOR PRINTING
       ======================================== */
    
    table.dataTable * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    /* ========================================
       MOBILE SPECIFIC OVERRIDES
       ======================================== */
    
    /* Force visibility on all screen sizes */
    @media (max-width: 768px) {
        .dtr-hidden,
        .d-none,
        .d-sm-none {
            display: table-cell !important;
            visibility: visible !important;
        }
    }
    
    @media (max-width: 576px) {
        .dtr-hidden,
        .d-none,
        .d-xs-none {
            display: table-cell !important;
            visibility: visible !important;
        }
    }
}

/* ========================================
   NON-PRINT HELPER CLASSES
   ======================================== */

/* Class to temporarily disable responsive behavior */
.disable-responsive .dtr-hidden {
    display: table-cell !important;
    visibility: visible !important;
}

.disable-responsive .dtr-control {
    display: none !important;
}
