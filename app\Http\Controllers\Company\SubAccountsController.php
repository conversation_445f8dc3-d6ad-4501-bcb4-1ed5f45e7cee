<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Models\AccountOperation;
use App\Models\MainAccount;
use App\Models\SubAccount;
use App\Models\Receipt;
use Yajra\DataTables\DataTables;
use App\Http\Requests\SubAccountsRequest;
use Auth;


class SubAccountsController extends Controller
{
    public function index()
    {
        $this->authorize('sub_accounts.index');
        $company=Auth::guard('company')->user();
        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }

       $mains=MainAccount::query()->get();

        if (request()->ajax())
        {
            $data = SubAccount::query()->where('company_id',$company_id)->orderByDesc('ordered')->latest();
            $search = request()->get('search', false);
            $main = request()->get('main', false);
            if(!empty($search) || $search != '') {

                $data = $data->where('name', 'like', "%{$search}%");
            }
            if(!empty($main) || $main != '') {
                $data = $data->where('parent_id',$main);
            }

//            $balance_sales = Invoice::query()->where('type_invoices','sales')->where('payment_status','paid')->where('company_id',$company_id)->orWhere('payment_status','partly_paid')->where('type_invoices','sales')->where('company_id',$company_id)->sum('payment_amount');
//
//            $sale_data=  [
//                'id'=>0,
//                'name'=> 'المبيعات',
//                'company_id'=>(string)$company_id,
//                'parent_id'=>'4',
//                "deleted_at"=> null,
//                "created_at"=> "2024-06-12T15:47:48.000000Z",
//                "updated_at"=>"2024-06-12T15:47:48.000000Z",
//                'balance'=>$balance_sales,
//                "parent_name"=> "الايرادات",
//
//            ];
//
//            $balance_purchases = Invoice::query()->where('type_invoices','purchases')->where('payment_status','paid')->where('company_id',$company_id)->orWhere('payment_status','partly_paid')->where('type_invoices','purchases')->where('company_id',$company_id)->sum('payment_amount');
//
//            $purchase_data=  [
//                'id'=>0,
//                'name'=> 'المشتريات',
//                'company_id'=>(string)$company_id,
//                'parent_id'=>'4',
//                "deleted_at"=> null,
//                "created_at"=> "2024-06-12T15:47:48.000000Z",
//                "updated_at"=>"2024-06-12T15:47:48.000000Z",
//                'balance'=>$balance_purchases,
//                "parent_name"=> "المصاريف",
//
//            ];
//
//
//            $data->push($sale_data);
//            $data->push($purchase_data);



            return DataTables::make($data)

                ->escapeColumns([])
                ->addColumn('actions', function ($row) {
                    return view('company.sub_accounts.buttons', compact('row'));
                })
                ->make();

        }

        return view('company.sub_accounts.index',compact('mains'));
    }


    public function create()
    {
        $this->authorize('sub_accounts.create');
        $main_accounts=MainAccount::query()->where('parent_id',0)->where('status','active')->get();
        return view('company.sub_accounts.create',compact('main_accounts'));
    }


    public function store(SubAccountsRequest $request)
    {
        $this->authorize('sub_accounts.create');
        $data = $request->validated();

        $main=MainAccount::query()->where('id',$request->parent_id)->first();
        $data['ordered']=$main->ordered;

        $data['company_id']=Auth::guard('company')->user()->id;


        $done= SubAccount::query()->create($data);


        if ($done->balance>0){

            $sub_account=SubAccount::query()->where('type_account','open_balance')->where('company_id', $data['company_id'])->first();

            if ($done->type=='creditor'){

                $receipt_data_open['amount']=$done->balance;
                $receipt_data_open['date']=$done->created_at;
                $receipt_data_open['details']='رصيد افتتاحي _'.$done->id;
                $receipt_data_open['sub_account']=$done->id;
                $receipt_data_open['main_account']=$done->parent_id;
                $receipt_data_open['type_bonds']='open_balance_account';
                $receipt_data_open['invoice_id']=0;
                $receipt_data_open['company_id']=$data['company_id'];


                ////

                $operation_data_open['creditor']=$done->balance;
                $operation_data_open['debtor']=0;
                $operation_data_open['date']=$done->created_at;
                $operation_data_open['details']='رصيد افتتاحي_'.$done->id;
                $operation_data_open['sub_account']=$done->id;
                $operation_data_open['source_id']=$done->id;
                $operation_data_open['invoice_id']=$done->id;
                $operation_data_open['type_receipts']='open_balance_account';
                $operation_data_open['company_id']=$data['company_id'];






                Receipt::query()->create($receipt_data_open);
                AccountOperation::query()->create($operation_data_open);


                $receipt_data_open1['amount']=$done->balance;
                $receipt_data_open1['date']=$done->created_at;
                $receipt_data_open1['details']='رصيد افتتاحي _'.$done->id;
                $receipt_data_open1['sub_account']=$sub_account->id;
                $receipt_data_open1['main_account']=$sub_account->parent_id;
                $receipt_data_open1['type_bonds']='open_balance_account';
                $receipt_data_open1['invoice_id']=0;
                $receipt_data_open1['company_id']=$data['company_id'];

                ////

                $operation_data_open1['creditor']=0;
                $operation_data_open1['debtor']=$done->balance;
                $operation_data_open1['date']=$done->created_at;
                $operation_data_open1['details']='رصيد افتتاحي_'.$done->id;
                $operation_data_open1['sub_account']=$sub_account->id;
                $operation_data_open1['source_id']=$done->id;
                $operation_data_open1['invoice_id']=$sub_account->id;
                $operation_data_open1['type_receipts']='open_balance_account';
                $operation_data_open1['company_id']=$data['company_id'];







                Receipt::query()->create($receipt_data_open1);
                AccountOperation::query()->create($operation_data_open1);



            }elseif ($done->type=='debtor'){

                $receipt_data_open2['amount']=$done->balance;
                $receipt_data_open2['date']=$done->created_at;
                $receipt_data_open2['details']='رصيد افتتاحي _'.$done->id;
                $receipt_data_open2['sub_account']=$done->id;
                $receipt_data_open2['main_account']=$done->parent_id;
                $receipt_data_open2['type_bonds']='open_balance_account';
                $receipt_data_open2['invoice_id']=0;
                $receipt_data_open2['company_id']=$data['company_id'];

                ////

                $operation_data_open2['creditor']=0;
                $operation_data_open2['debtor']=$done->balance;
                $operation_data_open2['date']=$done->created_at;
                $operation_data_open2['details']='رصيد افتتاحي_'.$done->id;
                $operation_data_open2['sub_account']=$done->id;
                $operation_data_open2['source_id']=$done->id;
                $operation_data_open2['invoice_id']=$done->id;
                $operation_data_open2['type_receipts']='open_balance_account';
                $operation_data_open2['company_id']=$data['company_id'];






                Receipt::query()->create($receipt_data_open2);
                AccountOperation::query()->create($operation_data_open2);


                $receipt_data_open3['amount']=$done->balance;
                $receipt_data_open3['date']=$done->created_at;
                $receipt_data_open3['details']='رصيد افتتاحي _'.$done->id;
                $receipt_data_open3['sub_account']=$done->id;
                $receipt_data_open3['main_account']=$sub_account->parent_id;
                $receipt_data_open3['type_bonds']='open_balance_account';
                $receipt_data_open3['invoice_id']=0;
                $receipt_data_open3['company_id']=$data['company_id'];

                ////

                $operation_data_open3['creditor']=$done->balance;
                $operation_data_open3['debtor']=0;
                $operation_data_open3['date']=$done->created_at;
                $operation_data_open3['details']='رصيد افتتاحي_'.$done->id;
                $operation_data_open3['sub_account']=$sub_account->id;
                $operation_data_open3['source_id']=$done->id;
                $operation_data_open3['invoice_id']=$sub_account->id;
                $operation_data_open3['type_receipts']='open_balance_account';
                $operation_data_open3['company_id']=$data['company_id'];







                Receipt::query()->create($receipt_data_open3);
                AccountOperation::query()->create($operation_data_open3);

            }else{


            }



        }


        return $this->redirectWith(false, 'company.sub_accounts.index', 'Successfully Added');
    }


    public function show(SubAccount $sub_account)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($sub_account)
    {
        $this->authorize('sub_accounts.edit');
        $sub_account=SubAccount::query()->findOrFail($sub_account);
        $main_account=MainAccount::query()->where('id',$sub_account->parent_id)->first();
        $main_accounts=MainAccount::query()->where('parent_id',0)->where('status','active')->get();
        $sub_accounts=MainAccount::query()->where('parent_id','!=',0)->where('status','active')->get();
        return view('company.sub_accounts.edit',compact('sub_account','sub_accounts','main_account','main_accounts'));

    }

    /**
     * Update the specified resource in storage.
     */
    public function update(SubAccountsRequest $request, SubAccount $sub_account)
    {
        $this->authorize('sub_accounts.edit');
         $data = $request->validated();
         $sub_account->update($data);

        $main=MainAccount::query()->where('id',$request->parent_id)->first();
        $data['ordered']=$main->ordered;

        $data['company_id']=Auth::guard('company')->user()->id;

        $account=SubAccount::query()->where('id', $sub_account->id)->first();
        $sub_account=SubAccount::query()->where('type_account','open_balance')->where('company_id', $account->company_id)->first();



        Receipt::query()->where('type_bonds','open_balance')->where('sub_account',$account->id)->where('company_id',$data['company_id'])->delete();
        Receipt::query()->where('type_bonds','open_balance')->where('sub_account',$sub_account->id)->where('company_id',$data['company_id'])->delete();

        AccountOperation::query()->where('type_receipts','open_balance_account')->where('source_id',$account->id)->where('company_id',$data['company_id'])->delete();



        if ($account->balance>0){

            $sub_account=SubAccount::query()->where('type_account','open_balance')->where('company_id', $account->company_id)->first();

            if ($account->type=='creditor'){

                $receipt_data_open['amount']=$account->balance;
                $receipt_data_open['date']=$account->created_at;
                $receipt_data_open['details']='رصيد افتتاحي _'.$account->id;
                $receipt_data_open['sub_account']=$account->id;
                $receipt_data_open['main_account']=$account->parent_id;
                $receipt_data_open['type_bonds']='open_balance_account';
                $receipt_data_open['invoice_id']=0;
                $receipt_data_open['company_id']=$data['company_id'];

                ////

                $operation_data_open['creditor']=$account->balance;
                $operation_data_open['debtor']=0;
                $operation_data_open['date']=$account->created_at;
                $operation_data_open['details']='رصيد افتتاحي_'.$account->id;
                $operation_data_open['sub_account']=$account->id;
                $operation_data_open['source_id']=$account->id;
                $operation_data_open['invoice_id']=$account->id;
                $operation_data_open['type_receipts']='open_balance_account';
                $operation_data_open['company_id']=$data['company_id'];






                Receipt::query()->create($receipt_data_open);
                AccountOperation::query()->create($operation_data_open);


                $receipt_data_open1['amount']=$account->balance;
                $receipt_data_open1['date']=$account->created_at;
                $receipt_data_open1['details']='رصيد افتتاحي _'.$account->id;
                $receipt_data_open1['sub_account']=$sub_account->id;
                $receipt_data_open1['main_account']=$sub_account->parent_id;
                $receipt_data_open1['type_bonds']='open_balance_account';
                $receipt_data_open1['invoice_id']=0;
                $receipt_data_open1['company_id']=$data['company_id'];

                ////

                $operation_data_open1['creditor']=0;
                $operation_data_open1['debtor']=$account->balance;
                $operation_data_open1['date']=$account->created_at;
                $operation_data_open1['details']='رصيد افتتاحي_'.$account->id;
                $operation_data_open1['sub_account']=$sub_account->id;
                $operation_data_open1['source_id']=$account->id;
                $operation_data_open1['invoice_id']=$sub_account->id;
                $operation_data_open1['type_receipts']='open_balance_account';
                $operation_data_open1['company_id']=$data['company_id'];







                Receipt::query()->create($receipt_data_open1);
                AccountOperation::query()->create($operation_data_open1);



            }elseif ($account->type=='debtor'){

                $receipt_data_open2['amount']=$account->balance;
                $receipt_data_open2['date']=$account->created_at;
                $receipt_data_open2['details']='رصيد افتتاحي _'.$account->id;
                $receipt_data_open2['sub_account']=$account->id;
                $receipt_data_open2['main_account']=$account->parent_id;
                $receipt_data_open2['type_bonds']='open_balance_account';
                $receipt_data_open2['invoice_id']=0;
                $receipt_data_open2['company_id']=$data['company_id'];

                ////

                $operation_data_open2['creditor']=0;
                $operation_data_open2['debtor']=$account->balance;
                $operation_data_open2['date']=$account->created_at;
                $operation_data_open2['details']='رصيد افتتاحي_'.$account->id;
                $operation_data_open2['sub_account']=$account->id;
                $operation_data_open2['source_id']=$account->id;
                $operation_data_open2['invoice_id']=$account->id;
                $operation_data_open2['type_receipts']='open_balance_account';
                $operation_data_open2['company_id']=$data['company_id'];






                Receipt::query()->create($receipt_data_open2);
                AccountOperation::query()->create($operation_data_open2);


                $receipt_data_open3['amount']=$account->balance;
                $receipt_data_open3['date']=$account->created_at;
                $receipt_data_open3['details']='رصيد افتتاحي _'.$account->id;
                $receipt_data_open3['sub_account']=$sub_account->id;
                $receipt_data_open3['main_account']=$sub_account->parent_id;
                $receipt_data_open3['type_bonds']='open_balance_account';
                $receipt_data_open3['invoice_id']=0;
                $receipt_data_open3['company_id']=$data['company_id'];

                ////

                $operation_data_open3['creditor']=$account->balance;
                $operation_data_open3['debtor']=0;
                $operation_data_open3['date']=$account->created_at;
                $operation_data_open3['details']='رصيد افتتاحي_'.$account->id;
                $operation_data_open3['sub_account']=$sub_account->id;
                $operation_data_open3['source_id']=$account->id;
                $operation_data_open3['invoice_id']=$sub_account->id;
                $operation_data_open3['type_receipts']='open_balance_account';
                $operation_data_open3['company_id']=$data['company_id'];







                Receipt::query()->create($receipt_data_open3);
                AccountOperation::query()->create($operation_data_open3);

            }else{


            }



        }

        return $this->redirectWith(false, 'company.sub_accounts.index', 'Successfully Added');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($sub_account)
    {
        $this->authorize('sub_accounts.delete');
        $company_id=Auth::guard('company')->user()->id;
        $data = SubAccount::query()->findOrFail($sub_account);

        if ($company_id==$data->company_id){
            $data->delete();
            return "success";
        }

    }

    public function get_sub_accounts($id){
        $sub=MainAccount::where('parent_id',$id)->get();
        return ['status'=>'success','sub'=>$sub];
    }

    public function operations_index($id)
    {
        $this->authorize('sub_accounts.show');
        $account_id=$id;
        $account = SubAccount::query()->findOrFail($account_id);
        if ($account){

            $balance=$account->balance;
        }

        $creditor=AccountOperation::query()->where('sub_account',$id)->sum('creditor');
        $debtor=AccountOperation::query()->where('sub_account',$id)->sum('debtor');

        if (request()->ajax())
        {

            $data = AccountOperation::query()->where('sub_account',$id)->latest();
            $search = request()->get('search', false);
            $status = request()->get('status', false);
            if(!empty($search) || $search != '') {
                $data = $data->where('name', 'like', '%' . $search. '%');
            }
            if(!empty($status) || $status != '') {
                $data = $data->where('status',$status);
            }
            return DataTables::make($data)
                ->escapeColumns([])
                ->make();
        }

        return view('company.sub_accounts.operations_index',compact('account_id','balance','creditor','debtor'));
    }
}
