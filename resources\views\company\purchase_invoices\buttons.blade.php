@if($row->is_return == 1)
    <button class="btn btn-icon btn-info" disabled>
        مرتجعة
    </button>
@endif
@can('expenses.index')
<a href="{{route('company.bills.invoice.expenses', $row->id)}}" class="btn btn-icon btn-success" title="سندات الصرف">
    <i class="fa-solid fa-list"></i>
</a>
@endcan
@if($row->change !=0 || $row->is_return ==1)
@can('purchases_invoice.payments')
<a href="{{route('company.invoice_purchases.create.payments', $row->id)}}" class="btn btn-icon btn-info" title="اضافة دفعة مالية">
    <i class="fa-solid fa-dollar-sign"></i>
</a>
@endcan
@endif
@can('purchases_invoice.show')
<a href="{{route('detail_purchases_invoice', $row->id)}}" class="btn btn-icon btn-secondary" title="تفاصيل الفاتورة">
    <i class="fa-solid fa-eye"></i>
</a>
@endcan
@can('purchases_invoice.delete')
<a  data-id="{{$row->id}}" data-toggle="modal" data-target="#deleteModel" class="deleteRecord btn btn-icon btn-danger" title="حذف الفاتورة">
    <i class="fa-solid fa-trash-can"></i>
</a>
@endcan

