<?php

namespace App;

use App\Models\CompanySubscription;
use App\Models\Setting;
use App\Models\Management;
use App\Models\SubscriptionPlan;
use App\Notifications\CompanyResetPassword;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;



class Company extends Authenticatable
{
    use Notifiable,HasRoles, HasPermissions;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'email', 'password','name_owner','name_manager','phone','location','city','postal_code',
        'tax_number','start_active','active_by','package_id','parent','status','job_title','job_number',
        'marital_status','image','logo','relative_name','relation','gender','rank','degree','educational','years_experience',
        'note','transfer_no','type_contract','annual_leave','nationality','iD_number','expiration_of_residency',
        'passport','expiration_passport','medical_number','expiration_medical_number','municipality_expires',
        'employee_custody','expiration_employee_custody','start_contract','end_contract','basic_salary','housing_allowance',
        'food_allowance','transfer_allowance','other_allowances','total_salary','file','phone_close','start_employee_custody',
        'bank','iban','tax','department','management','is_suadi'
    ];

    protected $apends=['department_name','management_name'];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new CompanyResetPassword($token));
    }

    /**
     * Route notifications for the mail channel.
     *
     * @return string
     */
    public function routeNotificationForMail()
    {
        return $this->email;
    }

    public function getImageAttribute($value)
    {
        return !is_null($value) ? asset($value):null;
    }

    public function getLogoAttribute($value)
    {
        if (!is_null($value) && file_exists(public_path($value))) {
            return asset($value);
        }
        return null;
    }
    
    // Add a method to get the raw logo path (without asset())
    public function getLogoPathAttribute()
    {
        return $this->attributes['logo'] ?? null;
    }

    /**
     * Get company logo with fallback to system logo
     *
     * @return string|null
     */
    public function getLogoWithFallback()
    {
        // First try to get company logo using raw path
        $logoPath = $this->attributes['logo'] ?? null;
        if ($logoPath && file_exists(public_path($logoPath))) {
            return asset($logoPath);
        }
        
        // Fallback to system logo
        $setting = \App\Models\Setting::first();
        if ($setting && $setting->logo && file_exists(public_path($setting->logo))) {
            return asset($setting->logo);
        }
        
        return null;
    }
    public function getDepartmentNameAttribute($value)
    {
        $main=Management::query()->where('id',$this->department)->first();
        if ($main) {

            return $main->name;
        }else{

            return  'لايوجد';
        }
    }
    public function getManagementNameAttribute($value)
    {
        $main=Management::query()->where('id',$this->management)->first();
        if ($main) {

            return $main->name;
        }else{

            return  'لايوجد';
        }
    }

    protected static function boot()
    {
        parent::boot();

        static::created(function ($company) {
            if ($company->parent) {
                return;
            }
            $permissions = Permission::query()->pluck('name')->toArray();
            $company->givePermissionTo($permissions);

            if (!$company->has_used_trial) {
                $company->activateTrial();
            }
        });
    }

    /**
     * Get all subscriptions for the company.
     */
    public function subscriptions()
    {
        return $this->hasMany(CompanySubscription::class);
    }

    /**
     * Get the active subscription for the company.
     */
    public function activeSubscription()
    {
        return $this->subscriptions()
            ->where('status', 'active')
            ->where('end_date', '>=', Carbon::today())
            ->latest('end_date')
            ->first();
    }

    /**
     * Check if the company has an active subscription.
     */
    public function hasActiveSubscription()
    {
        return $this->activeSubscription() !== null;
    }

    /**
     * Check if the company's subscription is expiring soon (within 3 days).
     */
    public function hasSubscriptionExpiringSoon()
    {
        $subscription = $this->activeSubscription();

        if (!$subscription) {
            return false;
        }

        return $subscription->isExpiringSoon();
    }

    /**
     * Activate a trial subscription for the company.
     */
    public function activateTrial()
    {
        $settings = Setting::first();
        $trialDays = $settings ? $settings->trial_period_days : 14;

        $this->subscriptions()->create([
            'start_date' => Carbon::today(),
            'end_date' => Carbon::today()->addDays($trialDays),
            'is_trial' => true,
            'status' => 'active'
        ]);

        $this->update([
            'has_used_trial' => true,
            'status' => 'active',
            'start_active' => Carbon::today()
        ]);

        return $this;
    }

    /**
     * Subscribe the company to a plan.
     */
    public function subscribeToPlan(SubscriptionPlan $plan, $paymentId = null)
    {
        $startDate = Carbon::today();

        // If there's an active subscription, start the new one after it ends
        $activeSubscription = $this->activeSubscription();
        if ($activeSubscription) {
            $startDate = Carbon::parse($activeSubscription->end_date)->addDay();
        }

        $subscription = $this->subscriptions()->create([
            'plan_id' => $plan->id,
            'start_date' => $startDate,
            'end_date' => $startDate->copy()->addDays($plan->duration_days),
            'is_trial' => false,
            'status' => 'active',
            'payment_id' => $paymentId
        ]);

        $this->update([
            'status' => 'active',
            'package_id' => $plan->id
        ]);

        if ($paymentId) {
            \App\Models\Receipt::create([
                'amount' => $plan->price,
                'date' => Carbon::today(),
                'details' => 'دفع اشتراك في باقة ' . $plan->name,
                'sub_account' => null, // Set appropriate account if needed
                'main_account' => null, // Set appropriate account if needed
                'type_bonds' => 'receipts',
                'company_id' => $this->id,
            ]);
        }

        return $this;
    }
}
