<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Auth;
use Carbon;


class Product extends Model
{

    use HasFactory, SoftDeletes ;
    protected $fillable = [
        'name','notes','category_id','image','status','company_id','supplier_id','manufacturing'
        ,'expiry_date','selling_price','purchasing_price','store_location','opening_balance',
        'brand','measruing_unit','tax','type','minimum'
    ];

    protected $appends =['category_name','sale_count','pay_count','sum_sale_price','sum_pay_price','pay_price','opening_balance_price','net_opening_balance','net_opening_balance_price','avareg_price'];
    protected $casts = [
        'created_at' => 'date:Y-m-d',
    ];


    public function getImageAttribute($value)
    {
        return !is_null($value) ? asset($value):null;
    }

    public function getCategoryNameAttribute()
    {
        $category=Category::query()->where('id',$this->category_id)->first();
        if ($category) {

            return $category->name;
        }else{

            return  'لايوجد';
        }
    }

    public function getSaleCountAttribute()
    {

        $company=Auth::guard('company')->user();
        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }

        $dataIds = Invoice::query()->where('type_invoices','sales')->where('company_id',$company_id)->pluck('id')->toArray();
        if (empty($dataIds)) {
            return 0;
        }

        $resaleIds=Resale::query()->where('product_id',0)->pluck('invoice_id')->toArray();

        $product = InvoiceProduct::query()
            ->whereIn('invoice_id', $dataIds)
            ->when(!empty($resaleIds), function ($query) use ($resaleIds) {
                return $query->whereNotIn('invoice_id', $resaleIds);
            })
            ->where('product_id', $this->id)
            ->sum('net_count');

        return $product ?? 0;

    }


    public function getNetOpeningBalanceAttribute(){


     return ($this->opening_balance+$this->getSaleCountAttribute())-$this->getPayCountAttribute();

    }
    public function getNetOpeningBalancePriceAttribute(){


     return $this->getNetOpeningBalanceAttribute() * $this->purchasing_price;

    }




    public function getSumSalePriceAttribute()
    {

        $company=Auth::guard('company')->user();
        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }
        $dataIds = Invoice::query()->where('type_invoices','sales')->where('company_id',$company_id)->pluck('id')->toArray();
        $resaleIds=Resale::query()->where('product_id',0)->pluck('invoice_id')->toArray();

        $product=InvoiceProduct::query()->whereIn('invoice_id',$dataIds)->whereNotIn('invoice_id',$resaleIds)->where('product_id',$this->id)->sum(\DB::raw('net_price * net_count'));


        // $tax_price=0.15*$product;
        // $net_amount=$product+$tax_price;
        return $product;

        return $net_amount;
    }

    public function getPayCountAttribute()
    {

        $company=Auth::guard('company')->user();
        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }
        $dataIds = Invoice::query()->where('type_invoices','purchases')->where('company_id',$company_id)->pluck('id')->toArray();

        if (empty($dataIds)) {
            return 0;
        }

        $repurchaseIds = Repurchase::query()->where('product_id',0)->pluck('invoice_id')->toArray();

        $product = InvoiceProduct::query()
            ->whereIn('invoice_id', $dataIds)
            ->when(!empty($repurchaseIds), function ($query) use ($repurchaseIds) {
                return $query->whereNotIn('invoice_id', $repurchaseIds);
            })
            ->where('product_id', $this->id)
            ->sum('net_count');

        return $product ?? 0;

    }




    public function getSumPayPriceAttribute()
    {

        $company=Auth::guard('company')->user();
        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }
        $dataIds = Invoice::query()->where('type_invoices','purchases')->where('company_id',$company_id)->pluck('id')->toArray();
        if (empty($dataIds)) {
            return 0;
        }
        $repurchaseIds = Repurchase::query()->where('product_id',0)->pluck('invoice_id')->toArray();

        $product = InvoiceProduct::query()
            ->whereIn('invoice_id', $dataIds)
            ->when(!empty($repurchaseIds), function ($query) use ($repurchaseIds) {
                return $query->whereNotIn('invoice_id', $repurchaseIds);
            })
            ->where('product_id', $this->id)
            ->sum(\DB::raw('net_price * net_count'));

        // $tax_price=0.15*$product;
        // $net_amount=$product+$tax_price;
        return $product ?? 0;

        return $net_amount;

    }

    public function getPayPriceAttribute()
    {

        $company=Auth::guard('company')->user();
        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }
        $dataIds = Invoice::query()->where('type_invoices','purchases')->where('company_id',$company_id)->pluck('id')->toArray();
        if (empty($dataIds)) {
            return 0;
        }
        $repurchaseIds = Repurchase::query()->pluck('invoice_id')->toArray();
        $product = InvoiceProduct::query()
            ->whereIn('invoice_id', $dataIds)
            ->when(!empty($repurchaseIds), function ($query) use ($repurchaseIds) {
                return $query->whereNotIn('invoice_id', $repurchaseIds);
            })
            ->where('product_id', $this->id)
            ->avg('price');


        return (int)$product ?? 0;

    }

    public function getOpeningBalancePriceAttribute()
    {

        $sum= $this->getNetOpeningBalancePriceAttribute()+$this->getSumPayPriceAttribute();
        $count= $this->getNetOpeningBalanceAttribute()+$this->getPayCountAttribute();
        if ($count>0){
            $nt= ($sum/$count)*$this->opening_balance;
        }else{
            $nt=0;
        }

        return $nt;
    }

      public function getAvaregPriceAttribute()
    {

        return $this->getPayPriceAttribute();
    }

    public function scopeType(Builder $query, $type) : Builder
    {
        return $query->where('type', $type);
    }







}
