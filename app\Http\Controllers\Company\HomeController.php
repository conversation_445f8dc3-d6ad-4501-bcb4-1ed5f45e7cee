<?php

namespace App\Http\Controllers\Company;

use App\Company;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Client;
use App\Models\Product;
use App\Models\Invoice;
use App\Models\Receipt;
use App\Models\InvoiceProductCart;
use App\Models\InvoiceProduct;
use App\Models\SubAccount;
use App\Models\AccountOperation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Http\Requests\SalesInvoiceRequest;

use DB;

class HomeController extends Controller
{



    public function home()
    {
        $company = Company::query()->findOrFail(auth('company')->id());
        $notifications = $company->notifications;
        $numOfUnreadNotifications = $company->unreadNotifications->count();

        // Get subscription data
        $activeSubscription = $company->activeSubscription();
        $hasSubscriptionExpiringSoon = $company->hasSubscriptionExpiringSoon();

        // Financial summary calculations
        $company_id = $company->id;

        // Only show statistics for main company (parent = 0)
        if ($company->parent == 0) {
            // Sales (total)
            $total_sales = \App\Models\Invoice::query()
                ->where('company_id', $company_id)
                ->where('type_invoices', 'sales')
                ->sum(\DB::raw('total_taxable + tax_amount'));

            // Purchases (total)
            $total_purchases = \App\Models\Invoice::query()
                ->where('company_id', $company_id)
                ->where('type_invoices', 'purchases')
                ->sum(\DB::raw('total_taxable + tax_amount'));

            // Expenses (total)
            $total_expenses = \App\Models\Expense::query()
                ->where('company_id', $company_id)
                ->sum('amount');

            // Employee Salaries (total)
            $total_salaries = \App\Models\Salary::query()
                ->where('company_id', $company_id)
                ->sum('advanced_salary_last'); // Adjust this field if you have a different salary field

            // Profit & Loss (total)
            $profit_loss = $total_sales - $total_purchases - $total_expenses - $total_salaries;

            // Chart Data: Sales & Purchases by Month
            $salesByMonth = \App\Models\Invoice::query()
                ->select(\DB::raw("DATE_FORMAT(date, '%Y-%m') as month"), \DB::raw('SUM(total_taxable + tax_amount) as total'))
                ->where('company_id', $company_id)
                ->where('type_invoices', 'sales')
                ->groupBy('month')
                ->orderBy('month')
                ->pluck('total', 'month');

            $purchasesByMonth = \App\Models\Invoice::query()
                ->select(\DB::raw("DATE_FORMAT(date, '%Y-%m') as month"), \DB::raw('SUM(total_taxable + tax_amount) as total'))
                ->where('company_id', $company_id)
                ->where('type_invoices', 'purchases')
                ->groupBy('month')
                ->orderBy('month')
                ->pluck('total', 'month');

            // Get all unique months from both sales and purchases
            $allMonths = collect($salesByMonth->keys())->merge($purchasesByMonth->keys())->unique()->sort()->values();
            
            // If no data exists, create sample data for the last 6 months
            if ($allMonths->isEmpty()) {
                $allMonths = collect();
                for ($i = 5; $i >= 0; $i--) {
                    $allMonths->push(now()->subMonths($i)->format('Y-m'));
                }
            }
            
            // Prepare data arrays for the chart
            $chartMonths = $allMonths->map(function($month) {
                // Convert YYYY-MM to Arabic month name
                $date = \Carbon\Carbon::createFromFormat('Y-m', $month);
                $arabicMonths = [
                    1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
                    5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
                    9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
                ];
                return $arabicMonths[$date->month] . ' ' . $date->year;
            });

            $salesData = $allMonths->map(function($month) use ($salesByMonth) {
                return $salesByMonth[$month] ?? 0;
            });

            $purchasesData = $allMonths->map(function($month) use ($purchasesByMonth) {
                return $purchasesByMonth[$month] ?? 0;
            });
        } else {
            // For sub-companies, set all values to 0
            $total_sales = 0;
            $total_purchases = 0;
            $total_expenses = 0;
            $total_salaries = 0;
            $profit_loss = 0;
            $chartMonths = collect();
            $salesData = collect();
            $purchasesData = collect();
        }

        return view('company.home', [
            'numOfUnreadNotifications' => $numOfUnreadNotifications,
            'notifications' => $notifications,
            'company' => $company,
            'activeSubscription' => $activeSubscription,
            'hasSubscriptionExpiringSoon' => $hasSubscriptionExpiringSoon,
            // Financial summary
            'total_sales' => $total_sales,
            'total_purchases' => $total_purchases,
            'total_expenses' => $total_expenses,
            'total_salaries' => $total_salaries,
            'profit_loss' => $profit_loss,
            // Chart data
            'chartMonths' => $chartMonths,
            'salesData' => $salesData,
            'purchasesData' => $purchasesData,
        ]);
    }

    public function invoice()
    {
        $company_id=Auth::guard('company')->user()->id;
        $clients=Client::query()->where('status','active')->get();
        $categories=Category::query()->where('status','active')->get();
        $products=Product::query()->where('status','active')->take(3)->latest()->get();
        $banks=SubAccount::query()->where('parent_id',7)->get();
        $cashs=SubAccount::query()->where('parent_id',6)->get();
        $invoice_products = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->get();
        $total_price=0;

        foreach ($invoice_products as $result){
            $total_price+= ($result->count*$result->price);

        }

        $tax_price=0.15*$total_price;
        $net_price=$total_price-$tax_price;

        return view('company.invoices.invoice',compact('clients','products','invoice_products','total_price',
                    'tax_price','net_price','categories','banks','cashs'));
    }

    public function add_product_to_invoice(Request $request)
    {

        $company_id=Auth::guard('company')->user()->id;

        $count = $request->count;
        $product_id = $request->product_id;
        $product = Product::query()->findOrFail($product_id);
        $product_price = $product->selling_price*$count;






        $test_invoice_in_cart=InvoiceProductCart::query()->where('company_id',$company_id)->where('product_id',$product_id)->where('type_invoice','sales')->first();










        if ($test_invoice_in_cart) {


                $data = response()->json([

                    'errors' => 'error here',
                    'product_id' => $product_id,
                    'message' => 'this product is exist',

                ]);


                return  $data;



        }else{








                $data['product_id']=$product_id;
                $data['count']=$count;
                $data['price']=$product->selling_price;
                $data['type_invoice']='sales';
                $data['company_id']=$company_id;

                $cart=InvoiceProductCart::query()->create($data);


                $total_price=0;
                $results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->get();

                foreach ($results as $result){
                    $total_price+= ($result->count*$result->price);

                }

                $tax_price=0.15*$total_price;
                $net_price=$total_price-$tax_price;





            $page = view('company.invoices.product', ['product' => $product,'count'=> $count,'product_price'=>$product_price,'cart_id' => $cart->id])->render();
                $data = response()->json([
                    'success' => true,
                    'message' => 'return success',
                    'errors' => 'error here',
                    'count' => $count,
                    'product_id' => $product_id,
                    'total_price' => $total_price,
                    'tax_price' => $tax_price,
                    'net_price' => $net_price,
                    'page' => $page,

                ]);


                return $data;


        }














    }

    public function post_invoice(SalesInvoiceRequest $request)
    {
        $company_id=Auth::guard('company')->user()->id;

        $data = $request->validated();
        $total_price=0;

        $results = InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->get();

        foreach ($results as $result){
            $total_price+= ($result->count*$result->price);

        }

        $tax_price=0.15*$total_price;
        $net_price=$total_price-$tax_price;

        if ($data['type_discount']=='percentage'){

            $discount_amount=($data['discount']/100)*$net_price;
        }else{

            $discount_amount=$data['discount'];
        }


        if ($data['payment_status']=='paid'){

            $data['payment_amount']=$net_price-$discount_amount;
        }


        $data['discount_amount']=$discount_amount;
        $data['total_amount']=$net_price;
        $data['net_amount']=$net_price-$discount_amount;
        $data['company_id']=$company_id;
        $data['created_by']=$company_id;





        $done= Invoice::query()->create($data);
        if ($done){

            foreach ($results as $result){

                $product_data['product_id']=$result->product_id;
                $product_data['invoice_id']=$done->id;
                $product_data['code_number']=$result->product_id;
                $product_data['count']=$result->count;
                $product_data['price']=$result->price;
                $product_data['total']=$result->count*$result->price;

                InvoiceProduct::query()->create($product_data);

            }


        }

         InvoiceProductCart::query()->where('company_id',$company_id)->where('type_invoice','sales')->delete();

        if ($done->payment_status=='paid'){

            $receipt_data['amount']=$done->net_amount;
            $receipt_data['tax_value']=$done->discount_amount;
            $receipt_data['date']=$done->date;
            $receipt_data['details']=$done->id.'فاتورة مشتريات رقم_';
            $receipt_data['sub_account']=$done->sub_account_id;
            $receipt_data['main_account']=$done->sub_account_id;
            $receipt_data['type_bonds']='receipts';
            ////

            $operation_data['creditor']=$done->net_amount;
            $operation_data['debtor']=0;
            $operation_data['date']=$done->date;
            $operation_data['details']=$done->id.'فاتورة مشتريات رقم_';
            $operation_data['sub_account']=$done->sub_account_id;

        }elseif ($done->payment_status=='partly_paid'){

            $receipt_data['amount']=$data['payment_amount'];
            $receipt_data['tax_value']=$done->discount_amount;
            $receipt_data['date']=$done->date;
            $receipt_data['details']=$done->id.'فاتورة مشتريات رقم_';
            $receipt_data['sub_account']=$done->sub_account_id;
            $receipt_data['main_account']=$done->sub_account_id;
            $receipt_data['type_bonds']='receipts';
            ////

            $operation_data['creditor']=$data['payment_amount'];
            $operation_data['debtor']=0;
            $operation_data['date']=$done->date;
            $operation_data['details']=$done->id.'فاتورة مشتريات رقم_';
            $operation_data['sub_account']=$done->sub_account_id;

        }

        Receipt::query()->create($receipt_data);
        AccountOperation::query()->create($operation_data);





        return $this->redirectWith(false, 'company.invoice', 'تمت الاضافة بنجاح');

    }



}
