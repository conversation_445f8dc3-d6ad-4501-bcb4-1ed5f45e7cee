
@if($row->is_return == 1)
    <button class="btn btn-icon btn-info" disabled>
        مرتجعة
    </button>
@endif
@can('receipts.index')
<a href="{{route('company.bills.invoice.receipts', $row->id)}}" class="btn btn-icon btn-success" title="سندات القبض">
    <i class="fa-solid fa-list"></i>
</a>
@endcan
@if($row->change !=0 && $row->is_return != 1 )
    @if($row->type != 'wating')
        @can('sales_invoice.payments')
<a href="{{route('company.invoice.create.payments', $row->id)}}" class="btn btn-icon btn-info" title="اضافة دفعة مالية">
    <i class="fa-solid fa-dollar-sign"></i>
</a>
        @endcan
@endif
@endif
@can('sales_invoice.show')
<a href="{{route('detail_sales_invoice', $row->id)}}" class="btn btn-icon btn-secondary" title="تفاصيل الفاتورة">
    <i class="fa-solid fa-eye"></i>
</a>
@endcan
@if($row->type == 'wating')
    @can('sales_invoice.edit')
    <a href="{{route('company.edit.sales.invoice', $row->id)}}" class="btn btn-icon btn-warning" title="تعديل الفاتورة">
        <i class="fa-solid fa-edit"></i>
    </a>
    @endcan
    @can('sales_invoice.delete')
    <a  data-id="{{$row->id}}" data-toggle="modal" data-target="#deleteModel" class="deleteRecord btn btn-icon btn-danger" title="حذف الفاتورة">
        <i class="fa-solid fa-trash-can"></i>
    </a>
    @endcan
@endif


