

<script>
    $(document).ready(function() {
        var table = $('#datatable');
        var isPrinting = false;

        // DataTable Configuration
        var dataTableConfig = {
            responsive: {
                details: {
                    type: 'column',
                    target: 'tr'
                }
            },
            processing: true,
            serverSide: true,
            ordering: false,
            searching: false,
            pageLength: 15,
            lengthMenu: [[10, 15, 25, 50, 100], [10, 15, 25, 50, 100]],
            ajax: {
                url: "<?php echo e($route); ?>",
                data: function(d) {
                    // Add filter parameters (check if elements exist)
                    d.from_date = $('#from_date').length ? $('#from_date').val() : '';
                    d.to_date = $('#to_date').length ? $('#to_date').val() : '';
                    d.search = $('#search').length ? $('#search').val() : '';

                    // Add custom parameters if provided
                    <?php if(isset($additionalParams)): ?>
                        <?php $__currentLoopData = $additionalParams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $param => $selector): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            d.<?php echo e($param); ?> = $('<?php echo e($selector); ?>').length ? $('<?php echo e($selector); ?>').val() : '';
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>

                    // If printing, request all data
                    if (isPrinting) {
                        d.length = -1; // Request all records
                        d.start = 0;
                    }
                }
            },
            columns: [
                // Sequence column (always first)
                {
                    data: null,
                    name: 'sequence',
                    orderable: false,
                    searchable: false,
                    render: function (data, type, row, meta) {
                        return meta.row + meta.settings._iDisplayStart + 1;
                    },
                    className: 'sequence-column text-center'
                },
                // Dynamic columns
                <?php if(isset($columns)): ?>
                    <?php $__currentLoopData = $columns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $column): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        {
                            data: '<?php echo e($column['data']); ?>',
                            name: '<?php echo e($column['name']); ?>',
                            <?php if(isset($column['render'])): ?>
                                render: <?php echo $column['render']; ?>,
                            <?php endif; ?>
                            <?php if(isset($column['className'])): ?>
                                className: '<?php echo e($column['className']); ?>',
                            <?php endif; ?>
                        },
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            ],
            columnDefs: [
                {
                    targets: 0, // Sequence column
                    responsivePriority: 1, // Highest priority - never hide
                    className: 'sequence-column text-center',
                    orderable: false,
                    searchable: false
                },
                {
                    targets: '_all',
                    responsivePriority: 2 // Lower priority for other columns
                },
                // Additional column definitions
                <?php if(isset($additionalColumnDefs)): ?>
                    <?php $__currentLoopData = $additionalColumnDefs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $def): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php echo json_encode($def); ?>,
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            ],
            dom: 'Bfrtip',
            buttons: [],
            // Additional configuration
            <?php if(isset($additionalConfig)): ?>
                <?php $__currentLoopData = $additionalConfig; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php echo e($key); ?>: <?php echo json_encode($value); ?>,
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
        };

        // Initialize DataTable
        console.log('Initializing DataTable with config:', dataTableConfig);
        var dataTable = table.DataTable(dataTableConfig);

        // Debug AJAX requests
        dataTable.on('xhr', function() {
            var json = dataTable.ajax.json();
            console.log('DataTable AJAX response:', json);
        });

        // Enhanced Print Function
        window.printSection = function() {
            if (isPrinting) return;
            
            const printBtn = document.querySelector('button[onclick="printSection()"]');
            if (!printBtn) return;
            
            const originalText = printBtn.innerHTML;
            printBtn.innerHTML = 'جاري الطباعة...';
            printBtn.disabled = true;
            isPrinting = true;
            
            // Store original responsive state
            const originalResponsive = dataTable.responsive;
            
            // Clear current table data and reload with all data
            dataTable.clear().draw();
            
            // Temporarily set page length to -1 to get all data
            const originalPageLength = dataTable.page.len();
            dataTable.page.len(-1);
            
            // Reload with all data
            dataTable.ajax.reload(function(json) {
                // Force all columns to be visible for printing
                if (originalResponsive && originalResponsive.hasHidden()) {
                    originalResponsive.recalc();
                    
                    const hiddenColumns = dataTable.columns('.dtr-hidden');
                    hiddenColumns.nodes().to$().removeClass('dtr-hidden').show();
                    
                    $('#datatable').addClass('print-ready');
                }
                
                // Fix sequence numbers and responsive issues for print
                setTimeout(function() {
                    // Ensure sequence column is visible
                    $('#datatable th:first-child, #datatable td:first-child').show();
                    
                    // Re-number all rows for continuous sequence in print
                    $('#datatable tbody tr').each(function(index) {
                        const firstCell = $(this).find('td:first-child');
                        firstCell.text(index + 1);
                        firstCell.removeClass('dtr-hidden').show();
                    });
                    
                    // Force responsive recalculation for mobile devices
                    if (window.innerWidth <= 768) {
                        dataTable.columns.adjust();
                        dataTable.responsive.recalc();
                    }
                    
                    // Trigger print
                    window.print();
                    
                    // Reset everything
                    printBtn.innerHTML = originalText;
                    printBtn.disabled = false;
                    isPrinting = false;
                    
                    $('#datatable').removeClass('print-ready');
                    
                    // Restore original page length and reload
                    dataTable.page.len(originalPageLength);
                    dataTable.ajax.reload(function() {
                        if (originalResponsive) {
                            originalResponsive.recalc();
                        }
                    });
                }, 150);
            });
        };

        // Filter Event Handlers (check if elements exist)
        if ($('#search').length) {
            $('#search').on('keyup', function() {
                dataTable.draw();
            });
        }

        if ($('#from_date').length || $('#to_date').length) {
            $('#from_date, #to_date').on('change', function() {
                dataTable.draw();
            });
        }

        // Additional filter handlers
        <?php if(isset($additionalFilters)): ?>
            <?php $__currentLoopData = $additionalFilters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $filter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                if ($('#<?php echo e($filter); ?>').length || $('select[name="<?php echo e($filter); ?>"]').length) {
                    $('#<?php echo e($filter); ?>, select[name="<?php echo e($filter); ?>"]').on('change', function() {
                        dataTable.draw();
                    });
                }
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    });
</script>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\dasta\resources\views/company/reports/components/datatable-script.blade.php ENDPATH**/ ?>