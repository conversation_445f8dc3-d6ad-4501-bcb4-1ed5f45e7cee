<?php

namespace App\Http\Controllers\Company;

use App\Company;
use App\Http\Controllers\Controller;
use App\Http\Requests\ListVacationRequest;
use App\Models\ListVacation;
use App\Models\Payroll;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Auth;


class ListVacationController extends Controller
{
    public function index()
    {
        $company=Auth::guard('company')->user();
        if ($company->parent==0){
            $company_id=$company->id;
        }else{
            $company_id=$company->parent;
        }

        if (request()->ajax())
        {
            $data = ListVacation::query()->where('created_by',$company_id)->latest();
            $search = request()->get('search', false);
            $status = request()->get('status', false);
            if(!empty($search) || $search != '') {
                $data = $data->where('name', 'like', '%' . $search. '%');
            }
            if(!empty($status) || $status != '') {
                $data = $data->where('status',$status);
            }
            return DataTables::make($data)
                ->escapeColumns([])
                ->addColumn('actions', function ($row) {
                    return view('company.list_vacations.buttons', compact('row'));
                })
                ->make();
        }

        return view('company.list_vacations.index');
    }


    public function create()
    {
        $employees= Company::query()->where('parent',Auth::guard('company')->user()->id)->get();
        return view('company.list_vacations.create',compact('employees'));
    }


    public function store(ListVacationRequest $request)
    {

        $data = $request->validated();
        $data['created_by']=Auth::guard('company')->user()->id;
        $data['date']=Carbon::now();
        ListVacation::query()->create($data);
        return $this->redirectWith(false, 'company.list_vacations.index', 'تمت الاضافة بنجاح');
    }


    public function show(Category $category)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($list_vacation)
    {
        $employees= Company::query()->where('parent',Auth::guard('company')->user()->id)->get();
        $list_vacations=ListVacation::query()->findOrFail($list_vacation);
        return view('company.list_vacations.edit',compact('list_vacations','employees'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(ListVacationRequest $request, ListVacation $list_vacation)
    {
        $data = $request->validated();
        $list_vacation->update($data);

        return $this->redirectWith(false, 'company.list_vacations.index', 'تم التعديل بنجاح');
    }


    public function print_vacations($id){

        $list_vacations=ListVacation::query()->findOrFail($id);
        return view('company.list_vacations.print',compact('list_vacations'));

    }
    public function filter_vacations($id){

        $list_vacations=ListVacation::query()->findOrFail($id);
        return view('company.list_vacations.print',compact('list_vacations'));

    }

    public function filter(Request $request){

        $from = $request->input('from');
        $to = $request->input('to');

        $company=Auth::guard('company')->user();
        $company_id=$company->id;
        $employee_arr_ids=Company::query()->where('parent',$company_id)->pluck('id')->toArray();
        $data = ListVacation::query()->whereIn('created_by',$employee_arr_ids)->whereBetween('date', [$from, $to])
            ->get();


        return view('company.list_vacations.filter',compact('data','from','to'));

    }


    /**
     * Remove the specified resource from storage.
     */
    public function destroy($category)
    {
        $data = ListVacation::query()->findOrFail($category);
        $company_id=Auth::guard('company')->user()->id;
        if ($company_id==$data->company_id){
            $data->delete();
            return "success";
        }

        return redirect()->back()->with('m-class', 'success')->with('message', t('تم الحذف بنجاح'));
    }
}
