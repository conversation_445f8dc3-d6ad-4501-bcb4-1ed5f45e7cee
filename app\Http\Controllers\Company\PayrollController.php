<?php

namespace App\Http\Controllers\Company;

use App\Company;
use App\Http\Controllers\Controller;
use App\Http\Requests\CategoryRequest;
use App\Http\Requests\MainAccountsRequest;
use App\Models\Category;
use App\Models\Payroll;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Auth;


class PayrollController extends Controller
{
    public function index()
    {

        $company=Auth::guard('company')->user();
        $company_id=$company->id;
        $employee_arr_ids=Company::query()->where('parent',$company_id)->pluck('id')->toArray();


        if (request()->ajax())
        {
            $data = Payroll::query()->whereIn('employee_id',$employee_arr_ids)->latest();
            $search = request()->get('search', false);
            $status = request()->get('status', false);
            if(!empty($search) || $search != '') {
                $data = $data->where('name', 'like', '%' . $search. '%');
            }
            if(!empty($status) || $status != '') {
                $data = $data->where('status',$status);
            }
            return DataTables::make($data)
                ->escapeColumns([])
                ->addColumn('actions', function ($row) {
                    return view('company.payrolls.buttons', compact('row'));
                })
                ->make();
        }

        return view('company.payrolls.index');
    }


    public function filter(Request $request){

        $from = $request->input('from');   // مثلاً 2025
        $to = $request->input('to'); // مثلاً 4

        $company=Auth::guard('company')->user();
        $company_id=$company->id;
        $employee_arr_ids=Company::query()->where('parent',$company_id)->pluck('id')->toArray();
        $data = Payroll::query()->whereIn('employee_id',$employee_arr_ids)->whereBetween('date', [$from, $to])
            ->get();


        return view('company.payrolls.print_filter',compact('data','from','to'));

    }



}
