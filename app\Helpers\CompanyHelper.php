<?php

namespace App\Helpers;

use App\Models\Setting;
use Illuminate\Support\Facades\Auth;

class CompanyHelper
{
    /**
     * Get the company logo with fallback to system logo
     *
     * @return string|null
     */
    public static function getCompanyLogo()
    {
        $setting = Setting::first();
        
        if (Auth::guard('company')->check()) {
            $company = Auth::guard('company')->user();
            $companyLogo = $company->logo_path; // Use raw path instead of processed logo
            
            // Check if company logo exists and is valid
            if ($companyLogo && file_exists(public_path($companyLogo))) {
                return asset($companyLogo); // Return full URL for display
            }
            
            // Fallback to system logo if it exists
            if ($setting && $setting->logo && file_exists(public_path($setting->logo))) {
                return asset($setting->logo);
            }
        } else {
            // Not authenticated, use system logo
            if ($setting && $setting->logo && file_exists(public_path($setting->logo))) {
                return asset($setting->logo);
            }
        }
        
        // Return null if no logo exists
        return null;
    }
    
    /**
     * Get the authenticated company
     *
     * @return \App\Company|null
     */
    public static function getAuthenticatedCompany()
    {
        return Auth::guard('company')->user();
    }
    
    /**
     * Check if user is authenticated as company
     *
     * @return bool
     */
    public static function isCompanyAuthenticated()
    {
        return Auth::guard('company')->check();
    }
} 